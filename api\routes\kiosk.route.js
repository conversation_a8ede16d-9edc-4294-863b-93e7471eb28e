const express = require("express");
const validate = require("../middlewares/validate");
const { KioskValidation, PatientGuestValidation } = require("../validations");
const { <PERSON><PERSON><PERSON><PERSON><PERSON>roller, PatientGuestController } = require("../controllers");
const auth = require("../middlewares/auth");
const catchAsync = require("../utils/catchAsync");

const router = express.Router();

/**
 * @swagger
 * /kiosk/device/{device_id}/setting:
 *   get:
 *     summary: Get device setting by device ID
 *     tags: [Kiosk]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: device_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The device ID to fetch settings for.
 *     responses:
 *       200:
 *         description: Device setting retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     device_setting_id:
 *                       type: string
 *                     device_id:
 *                       type: string
 *                     nda_id:
 *                       type: string
 *                     shownda:
 *                       type: boolean
 *                     showoutpatient:
 *                       type: boolean
 *                     showexpeditecheckin:
 *                       type: boolean
 *                     showwalkinguest:
 *                       type: boolean
 *       404:
 *         description: Device setting not found.
 */
router.get(
  "/device/:device_id/setting",
  auth("access_kiosk"),
  validate(KioskValidation.getDeviceSetting),
  catchAsync(KioskController.getDeviceSetting)
);

/**
 * @swagger
 * /kiosk/device/templates:
 *   get:
 *     summary: Get all device settings/templates
 *     tags: [Kiosk]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Device templates retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Device templates retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       device_setting_id:
 *                         type: string
 *                         format: uuid
 *                         description: Device setting ID
 *                       device_id:
 *                         type: string
 *                         format: uuid
 *                         description: Device ID
 *                       nda_id:
 *                         type: string
 *                         format: uuid
 *                         description: NDA template ID
 *                       shownda:
 *                         type: boolean
 *                         description: Show NDA option
 *                       showoutpatient:
 *                         type: boolean
 *                         description: Show outpatient option
 *                       showexpeditecheckin:
 *                         type: boolean
 *                         description: Show expedite check-in option
 *                       showwalkinguest:
 *                         type: boolean
 *                         description: Show walking guest option
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - Insufficient permissions
 */
router.get(
  "/device/templates",
  auth("access_kiosk"),
  validate(KioskValidation.getDeviceTemplate),
  catchAsync(KioskController.getDeviceTemplate)
);

/**
 * @swagger
 * /kiosk/guest/fetch:
 *   post:
 *     summary: Fetch guest by PIN and Name with device validation
 *     tags: [Kiosk]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - device_id
 *               - guest_pin
 *               - guest_name
 *             properties:
 *               device_id:
 *                 type: string
 *                 format: uuid
 *                 description: Device ID for validation and facility lookup
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               guest_pin:
 *                 type: string
 *                 description: Guest PIN for identification
 *                 example: "1234"
 *               guest_name:
 *                 type: string
 *                 description: Guest name (first name and/or last name)
 *                 example: "John Doe"
 *     responses:
 *       200:
 *         description: Guest found successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Guest found successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     appointment_guest_id:
 *                       type: string
 *                       format: uuid
 *                       description: Appointment guest ID
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                     guest_name:
 *                       type: string
 *                       description: Full guest name
 *                       example: "John Doe"
 *       400:
 *         description: Invalid device ID or validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid device ID"
 *       404:
 *         description: Guest not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No guest found with the provided PIN and name in Pending Check-In or Registered status"
 */
router.post(
  "/guest/fetch",
  auth("access_kiosk"),
  validate(KioskValidation.fetchGuestByPin),
  catchAsync(KioskController.fetchGuestByPin)
);

/**
 * @swagger
 * /kiosk/patient/details:
 *   post:
 *     summary: Get patient details and appointment guests by appointment_guest_id and phone last 4 digits
 *     tags: [Kiosk]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - device_id
 *               - appointment_guest_id
 *               - phone_last_4
 *             properties:
 *               device_id:
 *                 type: string
 *                 format: uuid
 *                 description: Device ID for validation and facility lookup
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               appointment_guest_id:
 *                 type: string
 *                 format: uuid
 *                 description: Appointment guest ID for identification
 *                 example: "456e7890-e89b-12d3-a456-************"
 *               phone_last_4:
 *                 type: string
 *                 pattern: "^\\d{4}$"
 *                 description: Last 4 digits of patient's phone number
 *                 example: "1234"
 *     responses:
 *       200:
 *         description: Patient and appointment guests retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Patient and appointment guests retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     patient_name:
 *                       type: string
 *                       description: Full patient name
 *                       example: "John Smith"
 *                     facility_name:
 *                       type: string
 *                       description: Facility name
 *                       example: "Main Hospital"
 *                     facility_id:
 *                       type: string
 *                       format: uuid
 *                       description: Facility ID
 *                     patient_details:
 *                       type: object
 *                       properties:
 *                         patient_id:
 *                           type: string
 *                           format: uuid
 *                         first_name:
 *                           type: string
 *                         last_name:
 *                           type: string
 *                         phone:
 *                           type: string
 *                     appointment_guests:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           appointment_guest_id:
 *                             type: string
 *                             format: uuid
 *                           guest_name:
 *                             type: string
 *                             example: "Jane Doe"
 *                           date_of_birth:
 *                             type: string
 *                             format: date
 *                             example: "1990-05-15"
 *                           email:
 *                             type: string
 *                             format: email
 *                             example: "<EMAIL>"
 *                           relation:
 *                             type: string
 *                             example: "Spouse"
 *       400:
 *         description: Invalid device ID or validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid device ID"
 *       404:
 *         description: Patient or appointment guest not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No appointment guest found with the provided ID and phone number in the specified facility"
 */
router.post(
  "/patient/details",
  auth("access_kiosk"),
  validate(KioskValidation.getPatientByAppointmentAndPhone),
  catchAsync(KioskController.getPatientByAppointmentAndPhone)
);

/**
 * @swagger
 * /kiosk/outpatient/details:
 *   post:
 *     summary: Get outpatient details by cellphone number and birth date
 *     tags: [Kiosk]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - device_id
 *               - cellphone
 *               - birth_date
 *             properties:
 *               device_id:
 *                 type: string
 *                 format: uuid
 *                 description: Device ID for validation and facility lookup
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               cellphone:
 *                 type: string
 *                 pattern: "^[\\+]?[1-9][\\d]{0,15}$"
 *                 description: Patient's cellphone number
 *                 example: "+**********"
 *               birth_date:
 *                 type: string
 *                 format: date
 *                 description: Patient's birth date (ISO format)
 *                 example: "1990-05-15"
 *     responses:
 *       200:
 *         description: Outpatient details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Outpatient details retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     facility_name:
 *                       type: string
 *                       description: Facility name
 *                       example: "Main Hospital"
 *                     facility_id:
 *                       type: string
 *                       format: uuid
 *                       description: Facility ID
 *                     patient_details:
 *                       type: object
 *                       properties:
 *                         patient_id:
 *                           type: string
 *                           format: uuid
 *                         name:
 *                           type: string
 *                           description: Full patient name
 *                           example: "John Smith"
 *                         birth_date:
 *                           type: string
 *                           format: date
 *                           example: "1990-05-15"
 *                         phone:
 *                           type: string
 *                           example: "+**********"
 *                     outpatient_appointments:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           appointment_id:
 *                             type: string
 *                             format: uuid
 *                           appointment_date:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-15T10:30:00.000Z"
 *                           patient_name:
 *                             type: string
 *                             example: "John Smith"
 *                           birth_date:
 *                             type: string
 *                             format: date
 *                             example: "1990-05-15"
 *       400:
 *         description: Invalid device ID or validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid device ID"
 *       404:
 *         description: No outpatient appointments found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No outpatient appointments found with the provided cellphone number and birth date"
 */
router.post(
  "/outpatient/details",
  auth("access_kiosk"),
  validate(KioskValidation.getOutpatientDetails),
  catchAsync(KioskController.getOutpatientDetails)
);

/**
 * @swagger
 * /kiosk/guest/add:
 *   post:
 *     summary: Add a guest to an appointment (Kiosk version)
 *     tags: [Kiosk]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - first_name
 *               - last_name
 *               - email
 *               - phone
 *               - guest_type
 *               - relationship_type
 *               - appointment_id
 *               - facility_id
 *             properties:
 *               first_name:
 *                 type: string
 *                 description: Guest's first name
 *                 example: "Jane"
 *               last_name:
 *                 type: string
 *                 description: Guest's last name
 *                 example: "Doe"
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Guest's email address
 *                 example: "<EMAIL>"
 *               phone:
 *                 type: string
 *                 description: Guest's phone number
 *                 example: "+**********"
 *               organization:
 *                 type: string
 *                 description: Guest's organization
 *                 example: "ABC Company"
 *               guest_type:
 *                 type: integer
 *                 description: Type of guest (from master data)
 *                 example: 1
 *               relationship_type:
 *                 type: integer
 *                 description: Relationship to patient (from master data)
 *                 example: 1
 *               related_person_name:
 *                 type: string
 *                 description: Name of related person
 *               related_person_contact:
 *                 type: string
 *                 description: Contact of related person
 *               relationship_status:
 *                 type: integer
 *                 description: Status of relationship
 *               is_emergency_contact:
 *                 type: boolean
 *                 description: Is this guest an emergency contact
 *                 example: false
 *               emergency_contact_priority:
 *                 type: integer
 *                 description: Priority as emergency contact
 *               can_make_decisions:
 *                 type: boolean
 *                 description: Can make medical decisions
 *                 example: false
 *               has_custody:
 *                 type: boolean
 *                 description: Has custody of patient
 *                 example: false
 *               lives_with_patient:
 *                 type: boolean
 *                 description: Lives with patient
 *                 example: true
 *               relationship_notes:
 *                 type: string
 *                 description: Notes about relationship
 *               effective_from:
 *                 type: string
 *                 format: date
 *                 description: Relationship effective from date
 *               effective_to:
 *                 type: string
 *                 format: date
 *                 description: Relationship effective to date
 *               reason:
 *                 type: string
 *                 description: Reason for guest (required when guest_type is 2)
 *               denied_on:
 *                 type: string
 *                 format: date-time
 *                 description: Date when denied
 *               appointment_id:
 *                 type: string
 *                 format: uuid
 *                 description: Appointment ID
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               start_date:
 *                 type: string
 *                 format: date
 *                 description: Visit start date
 *               start_time:
 *                 type: string
 *                 description: Visit start time
 *               duration:
 *                 type: integer
 *                 description: Visit duration in minutes
 *               escort_name:
 *                 type: string
 *                 description: Name of escort
 *               facility_id:
 *                 type: string
 *                 format: uuid
 *                 description: Facility ID
 *                 example: "456e7890-e89b-12d3-a456-************"
 *               screening:
 *                 type: string
 *                 description: Screening information
 *               image:
 *                 type: string
 *                 description: Guest image URL
 *               is_walkin:
 *                 type: boolean
 *                 description: Is walk-in guest
 *                 example: false
 *               friends_and_family:
 *                 type: boolean
 *                 description: Friends and family flag
 *                 example: true
 *               updated_by:
 *                 type: string
 *                 format: uuid
 *                 description: ID of user making the update
 *     responses:
 *       201:
 *         description: Guest added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Guest added successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     patientGuest:
 *                       type: object
 *                       description: Created patient guest record
 *                     appointmentGuest:
 *                       type: object
 *                       description: Created appointment guest record
 *       400:
 *         description: Validation error or missing required fields
 *       404:
 *         description: Appointment not found
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - Insufficient permissions
 */
router.post(
  "/guest/add",
  auth("access_kiosk"),
  validate(PatientGuestValidation.create),
  catchAsync(PatientGuestController.create)
);

/**
 * @swagger
 * /kiosk/checkin/patient-guest:
 *   post:
 *     summary: Perform patient and guest check-in together
 *     tags: [Kiosk]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - device_id
 *               - appointment_id
 *               - appointment_guest_id
 *             properties:
 *               device_id:
 *                 type: string
 *                 format: uuid
 *                 description: Device ID for validation and facility lookup
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               appointment_id:
 *                 type: string
 *                 format: uuid
 *                 description: Appointment ID for patient check-in
 *                 example: "456e7890-e89b-12d3-a456-************"
 *               appointment_guest_id:
 *                 type: string
 *                 format: uuid
 *                 description: Appointment guest ID for guest check-in
 *                 example: "789e0123-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: Patient and guest checked in successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Patient and guest checked in successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     facility_name:
 *                       type: string
 *                       description: Facility name
 *                       example: "Main Hospital"
 *                     facility_id:
 *                       type: string
 *                       format: uuid
 *                       description: Facility ID
 *                     check_in_time:
 *                       type: string
 *                       format: date-time
 *                       description: Time when check-in was performed
 *                       example: "2024-01-15T10:30:00.000Z"
 *                     patient_details:
 *                       type: object
 *                       properties:
 *                         appointment_id:
 *                           type: string
 *                           format: uuid
 *                         patient_id:
 *                           type: string
 *                           format: uuid
 *                         patient_name:
 *                           type: string
 *                           example: "John Smith"
 *                         appointment_date:
 *                           type: string
 *                           format: date-time
 *                           example: "2024-01-15T14:00:00.000Z"
 *                         status:
 *                           type: string
 *                           example: "Checked In"
 *                     guest_details:
 *                       type: object
 *                       properties:
 *                         appointment_guest_id:
 *                           type: string
 *                           format: uuid
 *                         guest_name:
 *                           type: string
 *                           example: "Jane Doe"
 *                         guest_pin:
 *                           type: string
 *                           example: "123456"
 *                         email:
 *                           type: string
 *                           format: email
 *                           example: "<EMAIL>"
 *                         phone:
 *                           type: string
 *                           example: "+**********"
 *                         status:
 *                           type: string
 *                           example: "Checked In"
 *       400:
 *         description: Invalid device ID, patient/guest already checked in, or validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   examples:
 *                     invalid_device:
 *                       value: "Invalid device ID"
 *                     patient_checked_in:
 *                       value: "Patient is already checked in"
 *                     guest_checked_in:
 *                       value: "Guest is already checked in"
 *       404:
 *         description: Appointment or appointment guest not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   examples:
 *                     appointment_not_found:
 *                       value: "Appointment not found or not accessible from this device's facility"
 *                     guest_not_found:
 *                       value: "Appointment guest not found or not associated with the provided appointment"
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - Insufficient permissions
 */
router.post(
  "/checkin/patient-guest",
  auth("access_kiosk"),
  validate(KioskValidation.performPatientGuestCheckin),
  catchAsync(KioskController.performPatientGuestCheckin)
);

/**
 * @swagger
 * /kiosk/inpatient/details:
 *   post:
 *     summary: Get inpatient appointment details by device, facility, phone last 4 digits, and name first 3 digits
 *     tags: [Kiosk]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - device_id
 *               - facility_id
 *               - phone_last_4
 *               - first_name_first_3
 *               - last_name_first_3
 *             properties:
 *               device_id:
 *                 type: string
 *                 format: uuid
 *                 description: Device ID for validation
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               facility_id:
 *                 type: string
 *                 format: uuid
 *                 description: Facility ID to search within
 *                 example: "456e7890-e89b-12d3-a456-************"
 *               phone_last_4:
 *                 type: string
 *                 pattern: "^\\d{4}$"
 *                 description: Last 4 digits of patient's phone number
 *                 example: "1234"
 *               first_name_first_3:
 *                 type: string
 *                 pattern: "^[a-zA-Z]{3}$"
 *                 description: First 3 characters of patient's first name
 *                 example: "Joh"
 *               last_name_first_3:
 *                 type: string
 *                 pattern: "^[a-zA-Z]{3}$"
 *                 description: First 3 characters of patient's last name
 *                 example: "Smi"
 *     responses:
 *       200:
 *         description: Inpatient appointment details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Inpatient appointment details retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     device_info:
 *                       type: object
 *                       properties:
 *                         device_id:
 *                           type: string
 *                           format: uuid
 *                         device_name:
 *                           type: string
 *                           example: "Kiosk Device 1"
 *                         facility_id:
 *                           type: string
 *                           format: uuid
 *                     search_criteria:
 *                       type: object
 *                       properties:
 *                         phone_last_4:
 *                           type: string
 *                           example: "1234"
 *                         first_name_first_3:
 *                           type: string
 *                           example: "Joh"
 *                         last_name_first_3:
 *                           type: string
 *                           example: "Smi"
 *                         appointment_type:
 *                           type: string
 *                           example: "Inpatient"
 *                     inpatient_appointments:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           appointment_id:
 *                             type: string
 *                             format: uuid
 *                           patient_name:
 *                             type: string
 *                             example: "John Smith"
 *                           facility_name:
 *                             type: string
 *                             example: "Main Hospital"
 *                           facility_id:
 *                             type: string
 *                             format: uuid
 *                           appointment_date:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-15T10:30:00.000Z"
 *                           status:
 *                             type: integer
 *                             description: Appointment status from master data
 *                             example: 1
 *                           patient_details:
 *                             type: object
 *                             properties:
 *                               patient_id:
 *                                 type: string
 *                                 format: uuid
 *                               first_name:
 *                                 type: string
 *                                 example: "John"
 *                               last_name:
 *                                 type: string
 *                                 example: "Smith"
 *                               phone:
 *                                 type: string
 *                                 example: "+**********"
 *                               date_of_birth:
 *                                 type: string
 *                                 format: date
 *                                 example: "1990-05-15"
 *       400:
 *         description: Invalid device ID, facility mismatch, or validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   examples:
 *                     invalid_device:
 *                       value: "Invalid device ID"
 *                     facility_mismatch:
 *                       value: "Facility ID does not match device's assigned facility"
 *       404:
 *         description: No inpatient appointments found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No inpatient appointments found with the provided criteria"
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - Insufficient permissions
 */
router.post(
  "/inpatient/details",
  auth("access_kiosk"),
  validate(KioskValidation.getInpatientAppointmentDetails),
  catchAsync(KioskController.getInpatientAppointmentDetails)
);

module.exports = router;
