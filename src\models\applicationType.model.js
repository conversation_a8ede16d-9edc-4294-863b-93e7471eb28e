const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const ApplicationType = sequelize.define(
    "ApplicationType",
    {
      application_type_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        comment: "machine-friendly identifier, e.g. 'api' or 'processor'",
      },
      display_name: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "human-friendly name, e.g. 'API' or 'HL7 Processor'",
      },
      description: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "application_type",
      timestamps: true,
      underscored: true,
      paranoid: true,
    }
  );

  history(ApplicationType, sequelize, DataTypes);

  return ApplicationType;
};
