const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const Floor = sequelize.define(
    "Floor",
    {
      floor_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      facility_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "facility",
          key: "facility_id",
        },
        onDelete: "CASCADE",
      },
      building_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "building",
          key: "building_id",
        },
        onDelete: "CASCADE",
      },
      floor_number: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      status: {
        // Changed from ENUM to INTEGER to store MasterData key.
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0, // assuming key 0 represents "Active"
      },
      total_square_footage: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      max_occupancy: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      occupancy_type: {
        // Changed from ENUM to INTEGER to store MasterData key.
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "floor",
      timestamps: true,
      underscored: true,
      // indexes: [
      //   {
      //     unique: true,
      //     fields: ["building_id", "floor_number"],
      //   },
      // ],
    }
  );

  Floor.associate = (models) => {
    Floor.belongsTo(models.Building, {
      foreignKey: "building_id",
      as: "building",
      onDelete: "CASCADE",
    });
    Floor.belongsTo(models.Facility, {
      foreignKey: "facility_id",
      as: "facility",
      onDelete: "CASCADE",
    });

  Floor.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "floor_status_name",
      constraints: false,
      scope: { group: "floor_status" },
    });

    Floor.belongsTo(models.MasterData, {
      foreignKey: "occupancy_type",
      targetKey: "key",
      as: "floor_occupancy_type_name",
      constraints: false,
      scope: { group: "floor_occupancy_type" },
    });
  };

  history(Floor, sequelize, DataTypes);

  return Floor;
};
