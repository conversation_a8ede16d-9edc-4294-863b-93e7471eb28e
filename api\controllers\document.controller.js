const httpStatus = require("http-status");
const { Document, Identity } = require("../models");
const catchAsync = require("../utils/catchAsync");
const { sendSuccess, sendError } = require("../utils/ApiResponse");
const { paginate } = require("../utils/plugins/paginate");
const { Op } = require("sequelize");

/**
 * @desc    Create a new document
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the created document
 */
exports.createDocument = catchAsync(async (req, res) => {
  // Clean up date fields - convert empty strings to null
  const cleanedData = { ...req.body };
  const dateFields = ['issue_date', 'expiration_date'];
  
  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });

  const document = await Document.create(cleanedData);
  sendSuccess(res, "Document created successfully", httpStatus.CREATED, document);
});

/**
 * @desc    Get all documents with pagination, sorting, and filtering
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing paginated documents
 */
exports.getDocuments = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, sortBy = "created_at", sortOrder = "DESC", search, identity_id, document_type, status } = req.query;
  const paginationOptions = { page, limit, sortBy, sortOrder };

  const queryOptions = {
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
    include: [
      {
        model: Identity,
        as: "identity",
        attributes: ["identity_id", "first_name", "last_name", "email"],
      },
    ],
  };

  // Build where conditions
  const whereConditions = {};
  
  if (identity_id) {
    whereConditions.identity_id = identity_id;
  }
  
  if (document_type) {
    whereConditions.document_type = document_type;
  }
  
  if (status) {
    whereConditions.status = status;
  }

  if (search) {
    whereConditions[Op.or] = [
      { document_number: { [Op.iLike]: `%${search}%` } },
      { document_type: { [Op.iLike]: `%${search}%` } },
      { country: { [Op.iLike]: `%${search}%` } },
      { state: { [Op.iLike]: `%${search}%` } },
      { other_issuer: { [Op.iLike]: `%${search}%` } },
    ];
  }

  if (Object.keys(whereConditions).length > 0) {
    queryOptions.where = whereConditions;
  }

  const result = await paginate(Document, queryOptions, paginationOptions);
  sendSuccess(res, "Documents retrieved successfully", httpStatus.OK, result);
});

/**
 * @desc    Get a single document by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the document
 */
exports.getDocumentById = catchAsync(async (req, res) => {
  const { document_id } = req.params;
  
  const document = await Document.findByPk(document_id, {
    include: [
      {
        model: Identity,
        as: "identity",
        attributes: ["identity_id", "first_name", "last_name", "email"],
      },
    ],
  });

  if (!document) {
    return sendError(res, "Document not found", httpStatus.NOT_FOUND);
  }

  sendSuccess(res, "Document retrieved successfully", httpStatus.OK, document);
});

/**
 * @desc    Update a document by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the updated document
 */
exports.updateDocument = catchAsync(async (req, res) => {
  const { document_id } = req.params;
  
  const document = await Document.findByPk(document_id);
  if (!document) {
    return sendError(res, "Document not found", httpStatus.NOT_FOUND);
  }

  // Clean up date fields - convert empty strings to null
  const cleanedData = { ...req.body };
  const dateFields = ['issue_date', 'expiration_date'];
  
  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });

  await document.update(cleanedData);
  sendSuccess(res, "Document updated successfully", httpStatus.OK, document);
});

/**
 * @desc    Delete a document by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object confirming deletion
 */
exports.deleteDocument = catchAsync(async (req, res) => {
  const { document_id } = req.params;
  
  const document = await Document.findByPk(document_id);
  if (!document) {
    return sendError(res, "Document not found", httpStatus.NOT_FOUND);
  }

  await document.destroy();
  sendSuccess(res, "Document deleted successfully", httpStatus.OK, { document_id: document_id });
});

