const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const AppointmentGuestScreening = sequelize.define(
    "AppointmentGuestScreening",
    {
      appointment_guest_screening_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      appointment_guest_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "appointment_guest",
          key: "appointment_guest_id",
        },
        onDelete: "CASCADE",
      },
      override: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      override_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      override_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      tableName: "appointment_guest_screening",
      timestamps: true,
      underscored: true,
    }
  );
  AppointmentGuestScreening.associate = (models) => {
    AppointmentGuestScreening.belongsTo(models.AppointmentGuest, {
      foreignKey: "appointment_guest_id",
      as: "appointmentGuest",
      onDelete: "CASCADE",
    });
    AppointmentGuestScreening.belongsTo(models.MasterData, {
      foreignKey: "override",
      targetKey: "key",
      as: "override_screening_name",
      constraints: false,
      scope: {
        group: "override_screening",
      },
    });
  };
  history(AppointmentGuestScreening, sequelize, DataTypes);
  return AppointmentGuestScreening;
};
