const logger = require("./config/logger");
const connectRabbitmq = require("./config/rabbitmq");
const PerformanceMonitor = require("./utils/performance");
const config = require("./config/config");
let middy;
let pLimitFn = null;

// Global state for graceful shutdown
let isShuttingDown = false;
let activeProcessingPromises = new Set();
let messageCollection = [];
let collectionTimer = null;
let collectionCounter = 0;
let channel = null;
let consumerTag = null;

// Message states for tracking during processing
const MESSAGE_STATES = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  SUCCESSFUL: 'successful',
  FAILED: 'failed'
};

// Track message states during processing
const messageStates = new Map(); // msg.properties.messageId -> state

// Graceful shutdown function
const gracefulShutdown = async (signal) => {
  if (isShuttingDown) {
    logger.warn(`Already shutting down, ignoring ${signal}`);
    return;
  }

  isShuttingDown = true;
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  try {
    // Stop accepting new messages
    if (consumerTag && channel) {
      logger.info('Cancelling RabbitMQ consumer...');
      await channel.cancel(consumerTag);
    }

    // Clear collection timer
    if (collectionTimer) {
      clearTimeout(collectionTimer);
      collectionTimer = null;
    }

    // Process any remaining messages in collection
    if (messageCollection.length > 0) {
      logger.info(`Processing remaining ${messageCollection.length} messages in collection...`);
      await processCollection(messageCollection);
      messageCollection = [];
    }

    // Wait for active processing to complete with timeout
    const shutdownTimeout = config.messageProcessing.shutdownTimeout || 30000;
    logger.info(`Waiting for ${activeProcessingPromises.size} active processing operations to complete (timeout: ${shutdownTimeout}ms)...`);

    if (activeProcessingPromises.size > 0) {
      const timeoutPromise = new Promise((resolve) => setTimeout(resolve, shutdownTimeout));
      const activePromise = Promise.all(Array.from(activeProcessingPromises));

      await Promise.race([activePromise, timeoutPromise]);

      if (activeProcessingPromises.size > 0) {
        logger.warn(`Shutdown timeout reached. ${activeProcessingPromises.size} operations still active.`);
      }
    }

    // Handle remaining messages based on their states
    await handleRemainingMessages();

    // Close RabbitMQ channel
    if (channel) {
      logger.info('Closing RabbitMQ channel...');
      await channel.close();
    }

    logger.info('Graceful shutdown completed');
    process.exit(0);

  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Handle remaining messages during shutdown
const handleRemainingMessages = async () => {
  const pendingMessages = [];
  const processingMessages = [];
  const failedMessages = [];

  for (const [messageId, state] of messageStates.entries()) {
    switch (state) {
      case MESSAGE_STATES.PENDING:
        pendingMessages.push(messageId);
        break;
      case MESSAGE_STATES.PROCESSING:
        processingMessages.push(messageId);
        break;
      case MESSAGE_STATES.FAILED:
        failedMessages.push(messageId);
        break;
      // SUCCESSFUL messages are already ack'd, no action needed
    }
  }

  logger.info(`Message states during shutdown - Pending: ${pendingMessages.length}, Processing: ${processingMessages.length}, Failed: ${failedMessages.length}`);

  // For pending and processing messages, we need to nack them so they return to queue
  // Only failed messages should be explicitly rejected (nack with requeue=false)
  // But since we want only failed messages to remain, we'll:
  // 1. Nack pending/processing messages with requeue=true (they go back to queue)
  // 2. Nack failed messages with requeue=false (they go to DLQ or are discarded)

  // Actually, based on the requirement, we want only FAILED messages to remain in RMQ
  // So we should:
  // 1. Ack successful messages (already done during processing)
  // 2. Nack pending/processing messages with requeue=false (remove from queue)
  // 3. Nack failed messages with requeue=true (keep in queue for retry)

  // This logic will be handled in the processCollection function
};

// Setup signal handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGHUP', () => gracefulShutdown('SIGHUP'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown('unhandledRejection');
});

(async () => {
  // Set up concurrency control
  const CONCURRENCY_LIMIT = config.messageProcessing.concurrencyLimit;
  if (CONCURRENCY_LIMIT > 0) {
    try {
      // Dynamically import ESM module
      const { default: pLimit } = await import('p-limit');
      pLimitFn = pLimit(CONCURRENCY_LIMIT);
      logger.info(`Concurrency limited to ${CONCURRENCY_LIMIT} messages at a time`);
    } catch (err) {
      logger.error('Failed to initialize p-limit. Falling back to unlimited concurrency.', err);
      pLimitFn = null;
    }
  }

  // Connect to RabbitMQ
  const { channel: rmqChannel } = await connectRabbitmq();
  channel = rmqChannel;

  // Now require models, caching functions, functions and middlewares after RabbitMQ is ready
  const { EventAction, EventTrace, TraceAction } = require("./models");
  const { getCachedFunction, getCachedApplication } = require("./utils/caching");
  const functionMappings = require("./functions");
  const internalMiddleware = require("./middlewares/internalMiddleware");
  const externalMiddleware = require("./middlewares/externalMiddleware");

  // Grab queue name from CLI args
  const queueArg = process.argv.find((arg) => arg.startsWith("--queue="));
  if (!queueArg) {
    logger.error("No queue specified. Usage: npm run dev -- --queue=queueName");
    process.exit(1);
  }
  const queueName = queueArg.split("=", 2)[1];
  if (!queueName) {
    logger.error("Invalid --queue flag. Usage: --queue=queueName");
    process.exit(1);
  }

  // Import middy dynamically
  const importedMiddy = await import("@middy/core");
  middy = importedMiddy.default;

  // Lookup the Function record (cached)
  const qf = await getCachedFunction({ queue: queueName });
  if (!qf) {
    logger.error(`No Function found for queue: ${queueName}. Discarding.`);
    return channel.ack(msg);
  }

  const functionId = qf.function_id;
  // Pull function name from qf, then find the actual handler
  const dynamicFunction = functionMappings[qf.name];
  if (!dynamicFunction) {
    logger.warn(`No mapping found for function: ${qf.name}`);
    return channel.ack(msg);
  }

  // Choose middleware based on type then Wrap + invoke
  let middleware;
  const application = await getCachedApplication({ id: qf.application_type_id });
  global.APP_CONTEXT = { function: qf.name, functionId, application: application.name };

  if (qf.type === "external") {
    middleware = externalMiddleware({
      application,
      function: qf,
      models: { EventTrace, TraceAction },
    });
  } else {
    middleware = internalMiddleware({
      application,
      functionId,
      models: { EventAction },
    });
  }

  // Helper to wrap dynamic function in middy + middleware
  const wrapDynamicHandler = (dynamicFunction, middleware) => {
    const baseHandler = async (event, context) => {
      return await dynamicFunction(event, context);
    };

    return middy(baseHandler).use(middleware);
  };

  // Create consumer on the queue
  await channel.assertQueue(queueName, { durable: true });
  logger.info(`Consuming messages from queue: ${queueName}`);
  logger.info(`Graceful shutdown enabled. Shutdown timeout: ${config.messageProcessing.shutdownTimeout}ms`);

  // Collection processing configuration
  const COLLECTION_TIMEOUT = config.messageProcessing.collectionTimeout;

  // Function to process a complete collection
  const processCollection = async (collection) => {
    if (collection.length === 0) return;

    // Check if shutting down
    if (isShuttingDown) {
      logger.info(`Processor is shutting down. Handling ${collection.length} messages appropriately...`);
      await handleShutdownCollection(collection);
      return;
    }

    collectionCounter++;
    const collectionId = `collection_${collectionCounter}_${Date.now()}`;

    // Initialize performance monitoring
    const performanceMonitor = new PerformanceMonitor(`MessageCollection_${queueName}_${collectionId}`);
    performanceMonitor.startStep('Collection Initialization', {
      collectionId,
      collectionSize: collection.length,
      queueName,
      functionName: qf.name
    });

    logger.info(`[COLLECTION ${collectionId}] Starting parallel processing of ${collection.length} messages`);

    const collectionResults = {
      collectionId,
      totalMessages: collection.length,
      successful: 0,
      failed: 0,
      errors: [],
      processingTimes: []
    };

    performanceMonitor.endStep('Collection Initialization');
    performanceMonitor.startStep('Parallel Message Processing', { totalMessages: collection.length });

    // Create all processing promises
    const processingPromises = collection.map(({ msg, event }, index) => {
      const messageIndex = index + 1;
      const messageStartTime = Date.now();
      const messageId = msg.properties.messageId || `msg_${collectionId}_${messageIndex}`;

      // Set initial state
      messageStates.set(messageId, MESSAGE_STATES.PENDING);

      // Define the processing function
      const processMessage = async () => {
        try {
          // Update state to processing
          messageStates.set(messageId, MESSAGE_STATES.PROCESSING);

          const handler = wrapDynamicHandler(dynamicFunction, middleware);

          // Pass collection performance monitor to the handler context
          const context = {
            collectionPerformanceMonitor: performanceMonitor,
            messageIndex: messageIndex,
            collectionId: collectionId
          };

          await handler(event, context);

          const messageProcessTime = Date.now() - messageStartTime;

          // Update state to successful
          messageStates.set(messageId, MESSAGE_STATES.SUCCESSFUL);

          return {
            success: true,
            msg,
            processingTime: messageProcessTime,
            messageIndex,
            messageId
          };
        } catch (err) {
          const messageProcessTime = Date.now() - messageStartTime;

          // Update state to failed
          messageStates.set(messageId, MESSAGE_STATES.FAILED);

          return {
            success: false,
            msg,
            error: err,
            processingTime: messageProcessTime,
            messageIndex,
            messageId
          };
        }
      };

      // Create promise and track it
      let promise;
      if (pLimitFn) {
        promise = pLimitFn(processMessage);
      } else {
        promise = processMessage();
      }

      // Add to active processing set
      promise.messageId = messageId;
      activeProcessingPromises.add(promise);

      // Remove from active set when done
      promise.finally(() => {
        activeProcessingPromises.delete(promise);
      });

      return promise;
    });

    // Process all messages concurrently
    const results = await Promise.allSettled(processingPromises);

    // Process results and handle acknowledgments
    for (const result of results) {
      if (result.status === 'fulfilled') {
        const { success, msg, processingTime, messageIndex, error, messageId } = result.value;

        if (success) {
          collectionResults.successful++;
          collectionResults.processingTimes.push(processingTime);

          // Acknowledge successful message
          if (!isShuttingDown) {
            channel.ack(msg);
          }

          performanceMonitor.addMetric('Message Processed Successfully', processingTime, {
            messageIndex,
            processingTimeMs: processingTime
          });

          // Clean up message state
          messageStates.delete(messageId);

        } else {
          collectionResults.failed++;
          collectionResults.errors.push({
            messageIndex,
            error: error.message,
            processingTimeMs: processingTime
          });
          collectionResults.processingTimes.push(processingTime);

          // Handle failed message based on shutdown state
          if (isShuttingDown) {
            // During shutdown, keep failed messages in queue for retry
            channel.nack(msg, false, true); // requeue = true
            logger.info(`[COLLECTION ${collectionId}] Failed message ${messageIndex} requeued due to shutdown`);
          } else {
            // Normal operation, reject failed message
            channel.nack(msg, false, false); // requeue = false
          }

          performanceMonitor.addMetric('Message Processing Failed', processingTime, {
            messageIndex,
            error: error.message,
            processingTimeMs: processingTime
          });
          logger.error(`[COLLECTION ${collectionId}] Error processing message ${messageIndex}:`, error);
        }
      } else {
        logger.error(`[COLLECTION ${collectionId}] Unexpected processing error:`, result.reason);

        // Handle unexpected errors - treat as failed
        const msg = collection[results.indexOf(result)]?.msg;
        if (msg) {
          if (isShuttingDown) {
            channel.nack(msg, false, true); // requeue = true
          } else {
            channel.nack(msg, false, false); // requeue = false
          }
        }
      }
    }

    performanceMonitor.endStep('Parallel Message Processing', {
      totalProcessed: collection.length,
      successful: collectionResults.successful,
      failed: collectionResults.failed
    });

    // Calculate collection statistics
    performanceMonitor.startStep('Collection Statistics Calculation');

    const avgProcessingTime = collectionResults.processingTimes.length > 0
      ? collectionResults.processingTimes.reduce((a, b) => a + b, 0) / collectionResults.processingTimes.length
      : 0;
    const maxProcessingTime = collectionResults.processingTimes.length > 0
      ? Math.max(...collectionResults.processingTimes)
      : 0;
    const minProcessingTime = collectionResults.processingTimes.length > 0
      ? Math.min(...collectionResults.processingTimes)
      : 0;
    const successRate = (collectionResults.successful / collectionResults.totalMessages) * 100;

    // Calculate elapsed time
    const currentTime = process.hrtime.bigint();
    const elapsedTimeMs = Number(currentTime - performanceMonitor.startTime) / 1e6;
    const throughputMessagesPerSecond = elapsedTimeMs > 0
      ? Math.round((collectionResults.totalMessages / (elapsedTimeMs / 1000)) * 100) / 100
      : 0;

    const finalCollectionResults = {
      ...collectionResults,
      statistics: {
        averageProcessingTimeMs: Math.round(avgProcessingTime * 100) / 100,
        maxProcessingTimeMs: maxProcessingTime,
        minProcessingTimeMs: minProcessingTime,
        successRate: Math.round(successRate * 100) / 100,
        throughputMessagesPerSecond: throughputMessagesPerSecond,
        totalElapsedTimeMs: Math.round(elapsedTimeMs * 100) / 100
      }
    };

    performanceMonitor.addMetric('Collection Success Rate', successRate, {
      successful: collectionResults.successful,
      failed: collectionResults.failed,
      total: collectionResults.totalMessages
    });

    performanceMonitor.addMetric('Average Processing Time', avgProcessingTime, {
      avgTimeMs: avgProcessingTime,
      maxTimeMs: maxProcessingTime,
      minTimeMs: minProcessingTime
    });

    performanceMonitor.endStep('Collection Statistics Calculation', finalCollectionResults.statistics);

    // Complete performance monitoring
    performanceMonitor.complete(finalCollectionResults);

    logger.info(`[COLLECTION ${collectionId}] Processing complete. Success: ${collectionResults.successful}, Failed: ${collectionResults.failed}, Success Rate: ${finalCollectionResults.statistics.successRate}%`);
    logger.info(`[COLLECTION ${collectionId}] Throughput: ${throughputMessagesPerSecond} msg/sec, Avg Time: ${finalCollectionResults.statistics.averageProcessingTimeMs}ms`);
    logger.info(`[COLLECTION ${collectionId}] Performance session ID: ${performanceMonitor.sessionId}`);
  };

  // Handle collection during shutdown
  const handleShutdownCollection = async (collection) => {
    logger.info(`Handling ${collection.length} messages during shutdown...`);

    for (const { msg } of collection) {
      const messageId = msg.properties.messageId || `shutdown_msg_${Date.now()}_${Math.random()}`;

      // For messages that haven't been processed yet during shutdown:
      // - We want to keep only FAILED messages in the queue
      // - Since these are unprocessed, we should nack them without requeue
      //   unless we want to give them a chance to be processed later

      // Based on the requirement, we should only retain FAILED messages
      // Since these are unprocessed, we'll nack them without requeue
      // This removes them from the queue
      messageStates.set(messageId, MESSAGE_STATES.PENDING);
      channel.nack(msg, false, false); // requeue = false, remove from queue

      logger.debug(`Shutdown: Removed unprocessed message from queue`);
    }

    logger.info(`Shutdown collection handling complete. ${collection.length} unprocessed messages removed from queue.`);
  };

  // Function to trigger collection processing
  const triggerCollectionProcessing = async () => {
    if (messageCollection.length > 0) {
      const currentCollection = [...messageCollection];
      messageCollection = []; // Clear the collection

      if (collectionTimer) {
        clearTimeout(collectionTimer);
        collectionTimer = null;
      }

      await processCollection(currentCollection);
    }
  };

  const consumer = await channel.consume(
    queueName,
    async (msg) => {
      if (!msg) return;

      // Don't accept new messages during shutdown
      if (isShuttingDown) {
        logger.debug('Rejecting new message due to shutdown');
        channel.nack(msg, false, true); // requeue = true
        return;
      }

      try {
        const raw = msg.content.toString();
        let event;
        try {
          event = JSON.parse(raw);
        } catch (_e) {
          event = raw;
        }

        // Add message to current collection
        messageCollection.push({ msg, event });

        // Set/reset timer for collection timeout
        if (collectionTimer) {
          clearTimeout(collectionTimer);
        }
        collectionTimer = setTimeout(triggerCollectionProcessing, COLLECTION_TIMEOUT);

      } catch (err) {
        logger.error(`Error adding message to collection:`, err);
        // Process individual message on collection error
        channel.nack(msg, false, false);
      }
    },
    { noAck: false }
  );

  // Store consumer tag for graceful shutdown
  consumerTag = consumer.consumerTag;
  logger.info(`Consumer started with tag: ${consumerTag}`);
})();
