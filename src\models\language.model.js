const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const Language = sequelize.define(
    "Language",
    {
      language_id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      code: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      default: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      status: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      addons: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "language",
      timestamps: false,
      hooks: {
        beforeUpdate: async (language, options) => {
          if (language.default) {
            // Unset default flag on all other languages
            await sequelize.models.Language.update(
              { default: false },
              {
                where: {
                  default: true,
                  language_id: { [sequelize.Op.ne]: language.language_id },
                },
                transaction: options.transaction,
              }
            );
          }
        },
      },
    }
  );

  // History plugin for audit trails
  history(Language, sequelize, DataTypes);

  // Associations
  Language.associate = (models) => {
    // A language can have many preferences
    Language.hasMany(models.LanguagePreference, {
      foreignKey: "language_id",
      as: "preference",
    });
  };

  return Language;
};
