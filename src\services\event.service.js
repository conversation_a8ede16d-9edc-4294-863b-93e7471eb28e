/**
 * Service to process events based on dynamic rules.
 */
const { v4: uuidv4 } = require("uuid");
const logger = require("../config/logger");
// const connectRabbitmq = require("../config/rabbitmq");
// const config = require("../config/config");

let channel = global.channel;

/**
 * Sends an event payload to RabbitMQ.
 * @param {Object} eventPayload - The event payload to send.
 * @param {Object} performanceMonitor - Optional performance monitor
 * @returns {Promise<void>}
 */
const sendToRabbitMQ = async (eventPayload, performanceMonitor = null) => {
  const startTime = Date.now();
  const payloadSize = Buffer.byteLength(JSON.stringify(eventPayload), 'utf8');

  try {
    // Get the current global channel
    channel = global.channel;

    if (!channel) {
      throw new Error("RabbitMQ channel is not available. Make sure RabbitMQ connection is established.");
    }

    // Track queue assertion time
    const queueAssertStart = Date.now();
    await channel.assertQueue(eventPayload.queue, {
      durable: true,
    });
    const queueAssertTime = Date.now() - queueAssertStart;

    // Track message sending time
    const messageSendStart = Date.now();
    channel.sendToQueue(eventPayload.queue, Buffer.from(JSON.stringify(eventPayload)), {
      persistent: true,
      priority: eventPayload.order,
    });
    const messageSendTime = Date.now() - messageSendStart;
    const totalTime = Date.now() - startTime;

    // Log performance metrics if monitor is available
    if (performanceMonitor) {
      performanceMonitor.addMetric('RabbitMQ Send Operation', {
        queue: eventPayload.queue,
        payloadSizeBytes: payloadSize,
        queueAssertTimeMs: queueAssertTime,
        messageSendTimeMs: messageSendTime,
        totalTimeMs: totalTime,
        priority: eventPayload.order
      });
    }

    logger.info(`Event sent to RabbitMQ queue: ${eventPayload.queue}`, {
      payloadSize: `${Math.round(payloadSize / 1024 * 100) / 100} KB`,
      sendTime: `${totalTime}ms`,
      batchSize: eventPayload.batch?.length || 'N/A'
    });
  } catch (error) {
    const errorTime = Date.now() - startTime;

    if (performanceMonitor) {
      performanceMonitor.addMetric('RabbitMQ Send Error', {
        queue: eventPayload.queue,
        error: error.message,
        timeToErrorMs: errorTime,
        payloadSizeBytes: payloadSize
      });
    }

    logger.error("Error sending event to RabbitMQ:", error);
    throw error;
  }
};

/**
 * Processes triggered events: saves them in the database and publishes them to RabbitMQ.
 * @param {Array} events - Array of triggered events from json-rules-engine.
 * @param {Object} instance - Model instance (parent object).
 * @param {string} traceId - Trace ID for tracking.
 * @param {Object} performanceMonitor - Optional performance monitor.
 * @returns {Promise<void>}
 */
const processEvents = async (events, instance, traceId, performanceMonitor = null) => {
  const { Event } = require("../models");
  const { getCachedEventConfig } = require("../utils/caching");

  // Sort events by the 'order', higher numeric value represents a higher priority
  events.sort((a, b) => b.order - a.order);

  for (const event of events) {
    const instanceData = instance.get
      ? instance.get({ plain: true })
      : instance;

    const primaryKeyField = instance.constructor.primaryKeyAttributes
      ? instance.constructor.primaryKeyAttributes[0]
      : "id";

    // Use the helper function
    const eventConfig = await getCachedEventConfig(event.type);
    if (!eventConfig) {
      logger.error(
        `Could not retrieve configuration for event type: ${event.type}`
      );
      continue;
    }

    const eventPayload = {
      event_id: uuidv4(),
      trace_id: traceId,
      parent_id: instanceData[primaryKeyField],
      child_id: null,
      event_type: event.type,
      params: event.params,
      order: eventConfig.order || 0,
      queue: eventConfig.queue,
    };

    await Event.create(eventPayload);
    await sendToRabbitMQ(eventPayload, performanceMonitor);
  }
};

/**
 * Processes triggered events in bulk: saves them in the database and publishes them to RabbitMQ.
 * @param {Array} bulkEvents - Array of bulk event data with instance and events.
 * @param {string} traceId - Trace ID for tracking.
 * @param {Object} performanceMonitor - Optional performance monitor.
 * @returns {Promise<void>}
 */
const processBulkEvents = async (bulkEvents, traceId, performanceMonitor = null) => {
  const { Event } = require("../models");
  const { getCachedEventConfig } = require("../utils/caching");

  if (bulkEvents.length === 0) return;

  const startTime = Date.now();
  const allEventPayloads = [];
  const eventConfigCache = new Map();

  // Prepare all event payloads
  for (const { events, instance } of bulkEvents) {
    // Sort events by the 'order', higher numeric value represents a higher priority
    events.sort((a, b) => b.order - a.order);

    for (const event of events) {
      const instanceData = instance.get
        ? instance.get({ plain: true })
        : instance;

      const primaryKeyField = instance.constructor.primaryKeyAttributes
        ? instance.constructor.primaryKeyAttributes[0]
        : "id";

      // Use cached event config to reduce database queries
      let eventConfig = eventConfigCache.get(event.type);
      if (!eventConfig) {
        eventConfig = await getCachedEventConfig(event.type);
        if (eventConfig) {
          eventConfigCache.set(event.type, eventConfig);
        }
      }

      if (!eventConfig) {
        logger.error(
          `Could not retrieve configuration for event type: ${event.type}`
        );
        continue;
      }

      const eventPayload = {
        event_id: uuidv4(),
        trace_id: traceId,
        parent_id: instanceData[primaryKeyField],
        child_id: null,
        event_type: event.type,
        params: event.params,
        order: eventConfig.order || 0,
        queue: eventConfig.queue,
      };

      allEventPayloads.push(eventPayload);
    }
  }

  if (allEventPayloads.length === 0) return;

  // Bulk create events in database
  await Event.bulkCreate(allEventPayloads);

  // Group events by queue for efficient RabbitMQ sending
  const queueGroups = new Map();
  allEventPayloads.forEach(payload => {
    if (!queueGroups.has(payload.queue)) {
      queueGroups.set(payload.queue, []);
    }
    queueGroups.get(payload.queue).push(payload);
  });

  // Send events to RabbitMQ by queue
  for (const [queue, payloads] of queueGroups) {
    for (const payload of payloads) {
      await sendToRabbitMQ(payload, performanceMonitor);
    }
  }

  const processingTime = Date.now() - startTime;

  if (performanceMonitor) {
    performanceMonitor.addMetric('Bulk Event Processing', processingTime, {
      totalEvents: allEventPayloads.length,
      uniqueQueues: queueGroups.size,
      processingTimeMs: processingTime
    });
  }

  logger.info(`Bulk processed ${allEventPayloads.length} events across ${queueGroups.size} queues in ${processingTime}ms`);
};

/**
 * Processes triggered events in bulk with individual trace contexts.
 * @param {Array} bulkEvents - Array of bulk event data with instance, events, and traceContext.
 * @param {Object} performanceMonitor - Optional performance monitor.
 * @returns {Promise<void>}
 */
const processBulkEventsWithTraces = async (bulkEvents, performanceMonitor = null) => {
  const { Event } = require("../models");
  const { getCachedEventConfig } = require("../utils/caching");

  if (bulkEvents.length === 0) return;

  const startTime = Date.now();
  const allEventPayloads = [];
  const eventConfigCache = new Map();

  // Prepare all event payloads with individual trace IDs
  for (const { events, instance, traceContext } of bulkEvents) {
    // Sort events by the 'order', higher numeric value represents a higher priority
    events.sort((a, b) => b.order - a.order);

    for (const event of events) {
      const instanceData = instance.get
        ? instance.get({ plain: true })
        : instance;

      const primaryKeyField = instance.constructor.primaryKeyAttributes
        ? instance.constructor.primaryKeyAttributes[0]
        : "id";

      // Use cached event config to reduce database queries
      let eventConfig = eventConfigCache.get(event.type);
      if (!eventConfig) {
        eventConfig = await getCachedEventConfig(event.type);
        if (eventConfig) {
          eventConfigCache.set(event.type, eventConfig);
        }
      }

      if (!eventConfig) {
        logger.error(
          `Could not retrieve configuration for event type: ${event.type}`
        );
        continue;
      }

      const eventPayload = {
        event_id: uuidv4(),
        trace_id: traceContext.trace_id,
        parent_id: instanceData[primaryKeyField],
        child_id: null,
        event_type: event.type,
        params: instance,
        order: eventConfig.order || 0,
        queue: eventConfig.queue,
      };

      allEventPayloads.push(eventPayload);
    }
  }

  if (allEventPayloads.length === 0) return;

  // Bulk create events in database
  await Event.bulkCreate(allEventPayloads);

  // Group events by queue for efficient RabbitMQ sending
  const queueGroups = new Map();
  allEventPayloads.forEach(payload => {
    if (!queueGroups.has(payload.queue)) {
      queueGroups.set(payload.queue, []);
    }
    queueGroups.get(payload.queue).push(payload);
  });

  // Send events to RabbitMQ by queue
  for (const [queue, payloads] of queueGroups) {
    for (const payload of payloads) {
      await sendToRabbitMQ(payload, performanceMonitor);
    }
  }

  const processingTime = Date.now() - startTime;

  if (performanceMonitor) {
    performanceMonitor.addMetric('Bulk Event Processing with Traces', processingTime, {
      totalEvents: allEventPayloads.length,
      uniqueQueues: queueGroups.size,
      processingTimeMs: processingTime
    });
  }

  logger.info(`Bulk processed ${allEventPayloads.length} events with individual traces across ${queueGroups.size} queues in ${processingTime}ms`);
};

/**
 * Runs the rules engine for a given model instance.
 * @param {Object} instance - The model instance.
 * @param {Object} rulesEngine - The rules engine instance.
 * @param {Set} rawFacts - Set of raw facts for the rules.
 * @param {Object} traceContext - Trace context information.
 * @param {Object} performanceMonitor - Optional performance monitor.
 * @returns {Promise<void>}
 */
const runRulesEngine = async ({
  instance,
  rulesEngine,
  rawFacts,
  traceContext = {},
  performanceMonitor = null
}) => {
  try {
    // Check if a trace_id exists; if not, generate one.
    let traceId = traceContext.trace_id || uuidv4();
    // If this is the top-level operation, record the trace metadata.
    if (!traceContext.recorded) {
      const { EventTrace } = require("../models");
      await EventTrace.create({
        trace_id: traceId,
        endpoint: traceContext.endpoint || null,
        function_id: traceContext.function_id || null
      });
      traceContext.recorded = true; // so future calls know that trace is recorded
    }

    // Build facts from the instance data
    const instanceData = instance.get
      ? instance.get({ plain: true })
      : instance;

    const changedFacts = new Set(
      [...rawFacts].filter(fact => fact.startsWith("changed."))
    );

    if (changedFacts.size > 0) {
      changedFacts.forEach((field) => {
        const fieldName = field.slice(8);
        if (instance._previousDataValues && instance._previousDataValues[fieldName] !== instance[fieldName]) {
          instanceData[`changed.${fieldName}`] = true;
        } else {
          instanceData[`changed.${fieldName}`] = false;
        }
      });
    }

    instanceData.endpoint = traceContext?.endpoint || null;
    instanceData.function = global.APP_CONTEXT?.function;
    instanceData.application = global.APP_CONTEXT?.application;

    const results = await rulesEngine.run(instanceData);
    await processEvents(results.events, instance, traceId, performanceMonitor);
  } catch (error) {
    logger.error("Error running rules engine:", error);
    throw error;
  }
};

/**
 * Runs the rules engine for multiple model instances in bulk with optimizations.
 * Creates EventTrace records in bulk and processes each instance individually.
 * @param {Array} instances - Array of model instances.
 * @param {Object} rulesEngine - The rules engine instance.
 * @param {Set} rawFacts - Set of raw facts for the rules.
 * @param {Object} ruleDependencyAnalysis - Analysis of rule dependencies.
 * @param {string} functionId - Function ID by which it is triggered.
 * @param {string} modelName - Name of the model for logging.
 * @param {Object} performanceMonitor - Optional performance monitor.
 * @returns {Promise<void>}
 */
const runBulkRulesEngine = async ({
  instances,
  rulesEngine,
  rawFacts,
  ruleDependencyAnalysis,
  functionId,
  modelName = 'Unknown',
  performanceMonitor = null
}) => {
  const startTime = Date.now();
  const instanceCount = instances.length;

  try {
    // Create EventTrace records in bulk for bulk operations
    const { EventTrace } = require("../models");
    const eventTraceData = instances.map(() => ({
      trace_id: uuidv4(),
      function_id: functionId,
    }));

    const eventTraces = await EventTrace.bulkCreate(eventTraceData);
    logger.info(`[BULK] Created ${eventTraces.length} EventTrace records in bulk for ${modelName}`);

    const bulkTraces = eventTraces.map(trace => trace.get({ plain: true }));
    const traceId = bulkTraces[0].trace_id || uuidv4();
    logger.info(`[BULK] Processing rules for ${instanceCount} instances with individual traces, base trace_id: ${traceId}`);

    const bulkEvents = [];
    const changedFacts = new Set(
      [...rawFacts].filter(fact => fact.startsWith("changed."))
    );

    // Process each instance individually (mimicking individual operations)
    // but collect all events for bulk processing
    for (let i = 0; i < instances.length; i++) {
      const instance = instances[i];
      const instanceTrace = bulkTraces[i];

      // Build facts from the instance data (same as individual operation)
      const instanceData = instance.get
        ? instance.get({ plain: true })
        : instance;

      // Handle changed facts (same as individual operation)
      if (changedFacts.size > 0) {
        changedFacts.forEach((field) => {
          const fieldName = field.slice(8);
          if (instance._previousDataValues && instance._previousDataValues[fieldName] !== instance[fieldName]) {
            instanceData[`changed.${fieldName}`] = true;
          } else {
            instanceData[`changed.${fieldName}`] = false;
          }
        });
      }

      instanceData.function = global.APP_CONTEXT?.function;
      instanceData.application = global.APP_CONTEXT?.application;

      // Run rules engine for this instance (same as individual operation)
      const results = await rulesEngine.run(instanceData);

      if (results.events.length > 0) {
        bulkEvents.push({
          instance,
          events: results.events,
          traceContext: instanceTrace
        });
      }
    }

    // Process all events in bulk (only difference from individual operations)
    if (bulkEvents.length > 0) {
      await processBulkEventsWithTraces(bulkEvents, performanceMonitor);
    }

    const processingTime = Date.now() - startTime;

    if (performanceMonitor) {
      performanceMonitor.addMetric('Bulk Rules Engine Processing', processingTime, {
        modelName,
        instanceCount,
        totalEvents: bulkEvents.length,
        independentRules: ruleDependencyAnalysis?.independentRules?.length || 0,
        dependentGroups: ruleDependencyAnalysis?.dependentGroups?.length || 0,
        processingTimeMs: processingTime
      });
    }

    logger.info(`[BULK] Processed rules for ${instanceCount} ${modelName} instances, generated ${bulkEvents.length} events in ${processingTime}ms`);

  } catch (error) {
    const errorTime = Date.now() - startTime;

    if (performanceMonitor) {
      performanceMonitor.addMetric('Bulk Rules Engine Error', errorTime, {
        modelName,
        instanceCount,
        error: error.message,
        timeToErrorMs: errorTime
      });
    }

    logger.error(`Error running bulk rules engine for ${modelName}:`, error);
    throw error;
  }
};

module.exports = {
  runRulesEngine,
  runBulkRulesEngine,
  sendToRabbitMQ,
};
