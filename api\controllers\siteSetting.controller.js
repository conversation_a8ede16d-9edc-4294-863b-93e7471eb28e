const cache = require("../config/caching");
const logger = require("../config/logger");
const { MasterData } = require("../models");
const { sendSuccess, sendError } = require("../utils/ApiResponse");
const httpStatus = require("http-status");
const { getCachedEventConfig } = require("../utils/caching");

/**
 * Retrieves cached data based on query parameters.
 *
 * @param {import('express').Request} req - Express request object, expecting a query parameter `keys` as an array.
 * @param {import('express').Response} res - Express response object.
 * @returns {Promise<import('express').Response>} Returns a JSON response with the cached data or an error message.
 */
exports.getCache = async (req, res) => {
  const { keys } = req.query;
  const results = {};
  await Promise.all(
    keys.map(async (key) => {
      results[key] = await cache.get(key);
    })
  );

  return sendSuccess(res, "Cache entries retrieved", httpStatus.OK, results);
};

/**
 * Performs cache operations (remove or refresh) based on the provided keys.
 * For a "refresh" operation, known keys (e.g., eventConfig and masterData) will be repopulated.
 *
 * @param {import('express').Request} req - Express request object, expecting a JSON body with:
 *   - operation: "refresh" or "remove"
 * @param {import('express').Response} res - Express response object.
 * @returns {Promise<import('express').Response>} Returns a JSON response with a success or error message.
 */
exports.setCache = async (req, res) => {
  const { operation, keys } = req.body;
  const errors = [];

  // Process specific keys
  await Promise.all(
    keys.map(async (key) => {
      try {
        await cache.delete(key);
        if (operation === "refresh") {
          if (key.startsWith("eventConfig:")) {
            const eventType = key.split(":")[1];
            await getCachedEventConfig(eventType);
          } else if (key.startsWith("masterData:")) {
            const group = key.split(":")[1];
            const data = await MasterData.findAll({
              where: { group },
              attributes: ["group", "key", "value"],
            });
            const groupData = data.map(({ key, value }) => ({ key, value }));
            await cache.set(key, groupData, 3600000);
          }
        }
      } catch (error) {
        logger.error(`Operation failed for ${key}:`, error);
        errors.push(key);
      }
    })
  );

  if (errors.length > 0) {
    return sendError(
      res,
      `Operations failed for keys: ${errors.join(", ")}`,
      httpStatus.MULTI_STATUS
    );
  }

  return sendSuccess(
    res,
    `Cache ${operation} completed successfully`,
    httpStatus.OK
  );
};
