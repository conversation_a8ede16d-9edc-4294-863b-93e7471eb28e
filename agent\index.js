const logger = require('./config/logger');
const config = require("./config/config");
const connectRabbitmq = require("./config/rabbitmq");
const { inboundAgentHandler } = require('./agents/inbound.agent');
const { outboundAgentHandler } = require('./agents/outbound.agent');

let connection, channel = global.channel;

// Parse command line arguments
const args = process.argv.slice(2);
const agentIndex = args.indexOf('--agent');
const agentName = agentIndex !== -1 && args[agentIndex + 1] ? args[agentIndex + 1] : null;

(async () => {
    // Await RabbitMQ connection first
    if (config.messageQueuing.status && !channel) {
        const rabbit = await connectRabbitmq();
        if (!rabbit || !rabbit.connection || !rabbit.channel) {
        throw new Error("RabbitMQ connect didn't return a valid object");
        }
        connection = rabbit.connection;
        channel = rabbit.channel;
        global.channel = channel; // Set global channel for event service
    }

    global.APP_CONTEXT = { function: null, application: "agent" };

    const { getCachedAgentConfigs } = require('./utils/caching');

    try {
        if (!agentName) {
            logger.error('Agent name is required. Usage: node index.js --agent <agent_name>');
            process.exit(1);
        }

        // Single agent mode: get agent by name
        logger.info(`Running agent: ${agentName}`);

        const targetAgent = await getCachedAgentConfigs({agentName});

        if (!targetAgent || !targetAgent.status) {
            logger.error(`No active agent found with name: ${agentName}`);
            process.exit(1);
        }

        logger.info(`Found agent: ${targetAgent.name} (Type: ${targetAgent.type}, Queue: ${targetAgent.queue})`);

        // Process based on agent type
        if (targetAgent.type === 'Inbound') {
            await inboundAgentHandler(null, targetAgent);
        } else if (targetAgent.type === 'Outbound') {
            // Generic outbound agent handles both CSV and API processing
            await outboundAgentHandler(targetAgent);
        } else {
            logger.error(`Unknown agent type: ${targetAgent.type}`);
            process.exit(1);
        }
        logger.info(`Agent processing completed for: ${agentName}`);
    } catch (err) {
        logger.error('Fatal error:', err);
        process.exit(1);
    }
})();
