[{"name": "Identity Modified Notification", "conditions": {"all": [{"fact": "function", "operator": "equal", "value": "parseData"}, {"fact": "email", "operator": "notEqual", "value": null}]}, "event": {"type": "notification", "params": {"notification": "Identity Created Notification"}}, "order": 1}, {"name": "Identity Modified Notification", "conditions": {"all": [{"fact": "function", "operator": "equal", "value": "parseData"}, {"fact": "email", "operator": "notEqual", "value": null}]}, "event": {"type": "api_1_outbound"}, "order": 1}, {"name": "Identity Modified Notification", "conditions": {"all": [{"fact": "function", "operator": "equal", "value": "parseData"}, {"fact": "email", "operator": "notEqual", "value": null}]}, "event": {"type": "api_2_outbound"}, "order": 1}]