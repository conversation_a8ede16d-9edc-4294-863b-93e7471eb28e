const models = require("../models");
const {
  sequelize,
  Hl7Message
} = models;
const logger = require("../config/logger");
const { hl7Event } = require("../validations/hl7.validation");
const mappingConfig = require("../mappings/hl7Message.mapping.json");
const { buildSpec, buildPayload, parseHL7Message } = require("../utils/hl7Helpers");

const spec = buildSpec(mappingConfig);

const hl7Store = async (eventInstance, context) => {
  const traceId = context.trace_id;
  logger.info(`[TRACE ${traceId}] Starting HL7 raw storing`);

  const cleaned = typeof eventInstance === "string" ? parseHL7Message(eventInstance) : eventInstance;

  const { error, value: validated } = hl7Event.body.validate(cleaned, {
    abortEarly: false,
    allowUnknown: true,
    stripUnknown: true,
  });
  if (error) {
    logger.error(`[TRACE ${traceId}] Validation failed: ${error.message}`);
    throw error;
  }

  const transaction = await sequelize.transaction();
  try {
    const msgType = (validated["MSH.9.1"] || "").toUpperCase();
    const eventCode = (validated["MSH.9.2"] || "").toUpperCase();

    const identifierData = await buildPayload(
      validated,
      spec.PatientIdentifier,
      "PatientIdentifier"
    );

    const hl7MessageData = {
      mrn: identifierData.identifier_value,
      message_type: msgType,
      hdr: eventCode,
      message: eventInstance,
      processed_at: Date.now(),
      updated_by: context.function.function_id,

    }

    await Hl7Message.create(hl7MessageData, {
      transaction,
    });

    await transaction.commit();
    logger.info(`[TRACE ${traceId}] Completed HL7 raw store`);
  } catch (err) {
    await transaction.rollback();
    logger.error(`[TRACE ${traceId}] Transaction rolled back: ${err.message}`);
    throw err;
  }
};

module.exports = hl7Store;
