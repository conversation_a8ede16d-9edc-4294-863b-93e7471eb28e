const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const HipaaEndpoint = sequelize.define(
    "HipaaEndpoint",
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      endpoint: {
        type: DataTypes.STRING(655),
        allowNull: false,
      
      },
    },
    {
      tableName: "hipaa_endpoints",
      timestamps: true,
      underscored: true,
    }
  );

  history(HipaaEndpoint, sequelize, DataTypes);

  return HipaaEndpoint;
};
