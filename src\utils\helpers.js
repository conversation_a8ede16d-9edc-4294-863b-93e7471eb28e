// D:\Windows\Desktop\caremate\caremate\api\utils\helpers.js

/**
 * Create an object composed of the picked object properties
 * @param {Object} object
 * @param {string[]} keys
 * @returns {Object}
 */
const pick = (object, keys) => {
  return keys.reduce((obj, key) => {
    if (object && Object.prototype.hasOwnProperty.call(object, key)) {
      obj[key] = object[key];
    }
    return obj;
  }, {});
};

/**
 * Converts a string to title case, where the first letter of each word is capitalized.
 *
 * @param {string} str - The input string to be converted to title case.
 * @returns {string} The title-cased version of the input string.
 */
const titleCase = (str) => {
  return str
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

/**
 * Generates a numeric One-Time Password (OTP) of a specified length.
 *
 * @param {number} [length=6] - The length of the OTP to generate. Defaults to 6 if not provided.
 * @returns {string} A randomly generated numeric OTP string.
 */
function generateOTP(length = 6) {
  const digits = "0123456789";
  let otp = "";
  for (let i = 0; i < length; i++) {
    otp += digits[Math.floor(Math.random() * 10)];
  }
  return otp;
}

/**
 * Get attribute keys from a Sequelize model.
 * @param {Object} model - Sequelize model
 * @returns {string[]}
 */
function getModelAttributes(model) {
  return Object.keys(model.rawAttributes || {});
}

/**
 * Transform boolean values to 'yes'/'no' strings based on model definition or data.
 * @param {Object} data - The data object to transform.
 * @param {Object|null} modelDefinition - Optional Sequelize model definition.
 * @returns {Object}
 */
function transformBooleans(data, modelDefinition = null) {
  if (!data || typeof data !== 'object') return data;

  const clone = { ...data };
  const booleanFields = [];

  if (modelDefinition?.rawAttributes) {
    Object.entries(modelDefinition.rawAttributes)
      .filter(([_, attr]) => attr.type.constructor.name === 'BOOLEAN')
      .forEach(([key]) => booleanFields.push(key));
  } else {
    Object.keys(clone)
      .filter(key => typeof clone[key] === 'boolean')
      .forEach(key => booleanFields.push(key));
  }

  booleanFields.forEach(key => {
    if (clone[key] !== undefined && clone[key] !== null) {
      clone[key] = clone[key] ? 'Yes' : 'No';
    }
  });

  return clone;
}

/**
 * Formats the identity details into the desired string format based on identity_id.
 * @param {string} identity_id - The ID of the identity to fetch.
 * @returns {Promise<string|null>}
 */
async function formatIdentity(identity_id) {
  if (!identity_id) return null;

  const { Identity, Function: FuncModel } = require('../models');
  const identity = await Identity.findOne({ where: { identity_id } });

  if (identity && identity.first_name && identity.last_name && identity.eid) {
    return `${identity.last_name} ${identity.first_name} (${identity.eid})`;
  }

  const func = await FuncModel.findOne({ where: { function_id: identity_id } });
  if (func && func.display_name) {
    return func.display_name;
  }

  return null;
}

/**
 * Create a new EventTrace record for every incoming request.
 * @param {import('express').Request} req
 * @returns {Promise<object>}
 */
async function createTraceContextByReq(req) {
  const { EventTrace, sequelize } = require('../models');
  const trx = req.transaction || await sequelize.transaction();

  // Only insert the endpoint field so the DB/Sequelize will generate the UUID
  const trace = await EventTrace.create(
    { endpoint: `${req.method}-${req.originalUrl.split('?')[0]}` },
    { transaction: trx, fields: ['endpoint'] }
  );

  return trace.get({ plain: true });
}

module.exports = {
  pick,
  titleCase,
  generateOTP,
  getModelAttributes,
  transformBooleans,
  formatIdentity,
  createTraceContextByReq,
};
