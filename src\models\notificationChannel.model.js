const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const NotificationChannel = sequelize.define(
    "NotificationChannel",
    {
      notification_channel_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      notification_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      channel: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      notification_child_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      tableName: "notification_channel",
      timestamps: true,
      underscored: true,
    }
  );

  NotificationChannel.associate = (models) => {
    NotificationChannel.belongsTo(models.Notification, {
      foreignKey: "notification_id",
      as: "notification",
    });
  };
  
  history(NotificationChannel, sequelize, DataTypes);

  return NotificationChannel;
};