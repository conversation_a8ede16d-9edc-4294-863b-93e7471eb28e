const express = require("express");
const auth = require("../middlewares/auth");
const validate = require("../middlewares/validate");
const DocumentValidation = require("../validations/document.validation");
const DocumentController = require("../controllers/document.controller");
const  uploadToBase64  = require("../middlewares/upload");
const catchAsync = require("../utils/catchAsync");

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Document:
 *       type: object
 *       required:
 *         - document_type
 *         - document_number
 *         - status
 *         - identity_id
 *       properties:
 *         document_id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the document
 *         document_type:
 *           type: integer
 *           description: Type of document
 *         document_number:
 *           type: string
 *           description: Document identification number
 *         status:
 *           type: integer
 *           description: Document status
 *         issue_date:
 *           type: string
 *           format: date
 *           description: Date when document was issued
 *         expiration_date:
 *           type: string
 *           format: date
 *           description: Date when document expires
 *         country:
 *           type: string
 *           description: Country of issuance
 *         state:
 *           type: string
 *           description: State/Province of issuance
 *         other_issuer:
 *           type: string
 *           description: Other issuing authority
 *         note:
 *           type: string
 *           description: Additional notes
 *         document:
 *           type: string
 *           description: Document file (base64 or file upload)
 *         identity_id:
 *           type: string
 *           format: uuid
 *           description: Reference to identity
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /documents:
 *   post:
 *     summary: Create a new document
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - document_type
 *               - document_number
 *               - status
 *               - identity_id
 *             properties:
 *               document_type:
 *                 type: integer
 *               document_number:
 *                 type: string
 *               status:
 *                 type: integer
 *               issue_date:
 *                 type: string
 *                 format: date
 *               expiration_date:
 *                 type: string
 *                 format: date
 *               country_id:
 *                 type: string
 *                 format: uuid
 *               state_id:
 *                 type: string
 *                 format: uuid
 *               other_issuer:
 *                 type: string
 *               note:
 *                 type: string
 *               document:
 *                 type: string
 *                 format: binary
 *               identity_id:
 *                 type: string
 *                 format: uuid
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Document'
 *     responses:
 *       201:
 *         description: Document created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/",
  auth("create_document"),
  uploadToBase64("document"),
  validate(DocumentValidation.createDocument),
  catchAsync(DocumentController.createDocument)
);

/**
 * @swagger
 * /documents:
 *   get:
 *     summary: Get all documents with pagination and filtering
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: identity_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by identity ID
 *       - in: query
 *         name: document_type
 *         schema:
 *           type: integer
 *         description: Filter by document type
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: Documents retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/",
  auth("view_document"),
  validate(DocumentValidation.getDocuments),
  catchAsync(DocumentController.getDocuments)
);

/**
 * @swagger
 * /documents/{document_id}:
 *   get:
 *     summary: Get a document by ID
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: document_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Document ID
 *     responses:
 *       200:
 *         description: Document retrieved successfully
 *       404:
 *         description: Document not found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/:document_id",
  auth("view_document"),
  validate(DocumentValidation.getDocumentById),
  catchAsync(DocumentController.getDocumentById)
);

/**
 * @swagger
 * /documents/{document_id}:
 *   put:
 *     summary: Update a document by ID
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: document_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Document ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               document_type:
 *                 type: integer
 *               document_number:
 *                 type: string
 *               status:
 *                 type: integer
 *               issue_date:
 *                 type: string
 *                 format: date
 *               expiration_date:
 *                 type: string
 *                 format: date
 *               country:
 *                 type: string
 *               state:
 *                 type: string
 *               other_issuer:
 *                 type: string
 *               note:
 *                 type: string
 *               document:
 *                 type: string
 *                 format: binary
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               document_type:
 *                 type: integer
 *               document_number:
 *                 type: string
 *               status:
 *                 type: integer
 *               issue_date:
 *                 type: string
 *                 format: date
 *               expiration_date:
 *                 type: string
 *                 format: date
 *               country_id:
 *                 type: string
 *                 format: uuid
 *               state_id:
 *                 type: string
 *                 format: uuid
 *               other_issuer:
 *                 type: string
 *               note:
 *                 type: string
 *               document:
 *                 type: string
 *     responses:
 *       200:
 *         description: Document updated successfully
 *       404:
 *         description: Document not found
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.put(
  "/:document_id",
  auth("edit_document"),
  uploadToBase64("document"),
  validate(DocumentValidation.updateDocument),
  catchAsync(DocumentController.updateDocument)
);

/**
 * @swagger
 * /documents/{document_id}:
 *   delete:
 *     summary: Delete a document by ID
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: document_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Document ID
 *     responses:
 *       200:
 *         description: Document deleted successfully
 *       404:
 *         description: Document not found
 *       401:
 *         description: Unauthorized
 */
router.delete(
  "/:document_id",
  auth("delete_document"),
  validate(DocumentValidation.deleteDocument),
  catchAsync(DocumentController.deleteDocument)
);



module.exports = router;
