// module.exports.authValidation = require('./auth.validation');
// module.exports.facilityValidation  = require('./facility.validation');
// module.exports.buildingValidation  = require('./building.validation');

const fs = require("fs");
const path = require("path");
const { titleCase } = require("../utils/helpers");

const validations = {};

// Get the current directory name (validations directory)
const validationsDirectory = __dirname;

// Read all files in the validations directory
fs.readdirSync(validationsDirectory)
  .filter((file) => file.endsWith(".validation.js")) // Only consider files ending with '.validation.js'
  .forEach((file) => {
    const validationName = titleCase(path.basename(file, ".validation.js")); // Remove the .validation.js extension for the validation name
    validations[`${validationName}Validation`] = require(path.join(validationsDirectory, file));
  });

// Set up associations (if defined in validations)
Object.keys(validations).forEach((validationName) => {
  if (validations[validationName].associate) {
    validations[validationName].associate(validations);
  }
});

module.exports = validations;