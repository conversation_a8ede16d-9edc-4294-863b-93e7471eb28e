const express = require("express");
const validate = require("../middlewares/validate");
const { FloorValidation } = require("../validations");
const { FloorController } = require("../controllers");
const auth = require("../middlewares/auth");
const catchAsync = require("../utils/catchAsync");

const router = express.Router({ mergeParams: true });

/**
 * @swagger
 * /facility/floors/{facilityId}:
 *   get:
 *     summary: Get all floors for a facility (paginated)
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID for which floors are retrieved.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *     responses:
 *       200:
 *         description: Paginated list of floors with facility details.
 *         content:
 *           application/json:
 *             example:
 *               totalItems: 15
 *               totalPages: 2
 *               currentPage: 1
 *               data:
 *                 - floor_id: "64b8f0e2d123e4567890abcd"
 *                   building_id: "1512dc4a-14a3-4917-99a7-8e688ddb82f5"
 *                   floor_number: 1
 *                   status: 0
 *                   total_square_footage: 2500.00
 *                   max_occupancy: 100
 *                   occupancy_type: 2
 *                   building_name: "Main Building"
 */
router.get("/", auth("view_floors"), validate(FloorValidation.facility), catchAsync(FloorController.index));

/**
 * @swagger
 * /facility/floors/{facilityId}/{floorId}:
 *   get:
 *     summary: Get a single floor by ID for a specific facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *       - in: path
 *         name: floorId
 *         required: true
 *         schema:
 *           type: string
 *         description: The floor ID.
 *     responses:
 *       200:
 *         description: Floor details with facility name.
 *         content:
 *           application/json:
 *             example:
 *               floor_id: "64b8f0e2d123e4567890abcd"
 *               building_id: "1512dc4a-14a3-4917-99a7-8e688ddb82f5"
 *               floor_number: 2
 *               status: 0
 *               total_square_footage: 3000.00
 *               max_occupancy: 120
 *               occupancy_type: 2
 *               building_name: "Main Building"
 *       404:
 *         description: Floor not found.
 */
router.get("/:floorId", auth("floor_details"), validate(FloorValidation.floor), catchAsync(FloorController.show));

/**
 * @swagger
 * /facility/floors/{facilityId}:
 *   post:
 *     summary: Create a new floor in a facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID where the floor will be added.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             floor_number: 3
 *             total_square_footage: 3500.50
 *             max_occupancy: 150
 *             occupancy_type: 2
 *             building_id: "1512dc4a-14a3-4917-99a7-8e688ddb82f5"
 *             status: 0
 *     responses:
 *       201:
 *         description: Floor created successfully.
 */
router.post("/", auth("create_floor"), validate(FloorValidation.create), catchAsync(FloorController.create));

/**
 * @swagger
 * /facility/floors/{facilityId}/{floorId}:
 *   patch:
 *     summary: Update a floor's details
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *       - in: path
 *         name: floorId
 *         required: true
 *         schema:
 *           type: string
 *         description: The floor ID.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             floor_number: 4
 *             total_square_footage: 4000.75
 *             max_occupancy: 180
 *             occupancy_type: 2
 *             building_id: "1512dc4a-14a3-4917-99a7-8e688ddb82f5"
 *             status: 0
 *     responses:
 *       200:
 *         description: Floor updated successfully.
 */
router.patch("/:floorId", auth("edit_floor"), validate(FloorValidation.update), catchAsync(FloorController.update));

/**
 * @swagger
 * /facility/floors/{facilityId}/{floorId}/status:
 *   patch:
 *     summary: Change floor status
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *       - in: path
 *         name: floorId
 *         required: true
 *         schema:
 *           type: string
 *         description: The floor ID.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             status: 1
 *     responses:
 *       200:
 *         description: Floor status updated successfully.
 */
router.patch("/:floorId/status", auth("status_floor"), validate(FloorValidation.status), catchAsync(FloorController.status));

module.exports = router;
