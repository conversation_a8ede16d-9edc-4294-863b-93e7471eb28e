const httpStatus = require("http-status");
const { Delegates, Identity } = require("../models");
const catchAsync = require("../utils/catchAsync");
const { sendSuccess, sendError } = require("../utils/ApiResponse");
const { paginate } = require("../utils/plugins/paginate");
const { Op } = require("sequelize");

/**
 * @desc    Create a new delegation
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the created delegation
 */
exports.createDelegate = catchAsync(async (req, res) => {
  const cleanedData = { ...req.body };
  const dateFields = ['start_date', 'end_date'];
  
  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });
  const delegate = await Delegates.create(cleanedData);
  sendSuccess(res, "Delegation created successfully", httpStatus.CREATED, delegate);
});

/**
 * @desc    Get all delegations with pagination, sorting, and filtering
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing paginated delegations
 */
exports.getDelegates = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, sortBy = "created_at", sortOrder = "DESC", search, identity_id, status } = req.query;
  const paginationOptions = { page, limit, sortBy, sortOrder };

  const queryOptions = {
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
    include: [
      {
        model: Identity,
        as: "identity",
        attributes: ["identity_id", "first_name", "last_name", "email"],
      },
    ],
  };

  // Build where conditions
  const whereConditions = {};
  
  if (identity_id) {
    whereConditions.identity_id = identity_id;
  }
  
  if (status) {
    whereConditions.status = status;
  }

  if (search) {
    whereConditions[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { task_to_delegate: { [Op.iLike]: `%${search}%` } },
    ];
  }

  if (Object.keys(whereConditions).length > 0) {
    queryOptions.where = whereConditions;
  }

  const result = await paginate(Delegates, queryOptions, paginationOptions);
  sendSuccess(res, "Delegations retrieved successfully", httpStatus.OK, result);
});

/**
 * @desc    Get a single delegation by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the delegation
 */
exports.getDelegateById = catchAsync(async (req, res) => {
  const { delegate_id } = req.params;
  
  const delegate = await Delegates.findByPk(delegate_id, {
    include: [
      {
        model: Identity,
        as: "identity",
        attributes: ["identity_id", "first_name", "last_name", "email"],
      },
    ],
  });

  if (!delegate_id) {
    return sendError(res, "Delegation not found", httpStatus.NOT_FOUND);
  }

  sendSuccess(res, "Delegation retrieved successfully", httpStatus.OK, delegate);
});

/**
 * @desc    Update a delegation by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the updated delegation
 */
exports.updateDelegate = catchAsync(async (req, res) => {
  const { delegate_id } = req.params;
  
  const delegate = await Delegates.findByPk(delegate_id);
  if (!delegate) {
    return sendError(res, "Delegation not found", httpStatus.NOT_FOUND);
  }

  // Clean up date fields - convert empty strings to null
  const cleanedData = { ...req.body };
  const dateFields = ['start_date', 'end_date'];
  
  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });

  await delegate.update(cleanedData);
  sendSuccess(res, "Delegation updated successfully", httpStatus.OK, delegate);
});

/**
 * @desc    Delete a delegation by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object confirming deletion
 */
exports.deleteDelegate = catchAsync(async (req, res) => {
  const { delegate_id } = req.params;
  
  const delegate = await Delegates.findByPk(delegate_id);
  if (!delegate) {
    return sendError(res, "Delegation not found", httpStatus.NOT_FOUND);
  }

  await delegate.destroy();
  sendSuccess(res, "Delegation deleted successfully", httpStatus.OK, { delegate_id: delegate_id });
});


