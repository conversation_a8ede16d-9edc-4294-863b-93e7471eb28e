const models = require("../models");
const {
  sequelize,
  Patient,
  PatientIdentifier,
  Appointment,
  EventTrace,
  Hl7Message
} = models;
const logger = require("../config/logger");
const { hl7Event } = require("../validations/hl7.validation");
const mappingConfig = require("../mappings/hl7Message.mapping.json");
const {
  ADT_TYPE_STATUS_MAP,
  SIU_TYPE_STATUS_MAP
} = require("../mappings/hl7TypeStatus.mapping.json");
const { buildSpec, buildPayload, parseHL7Message } = require("../utils/hl7Helpers");

const spec = buildSpec(mappingConfig);

const hl7Processor = async (eventInstance, context) => {
  const traceId = context.trace_id;
  logger.info(`[TRACE ${traceId}] Starting HL7 processing`);

  const cleaned = typeof eventInstance === "string" ? parseHL7Message(eventInstance) : eventInstance;
  const { error, value: validated } = hl7Event.body.validate(cleaned, {
    abortEarly: false,
    allowUnknown: true,
    stripUnknown: true,
  });
  if (error) {
    logger.error(`[TRACE ${traceId}] Validation failed: ${error.message}`);
    throw error;
  }

  const transaction = await sequelize.transaction();
  try {
    const msgType = (validated["MSH.9.1"] || "").toUpperCase();
    const eventCode = (validated["MSH.9.2"] || "").toUpperCase();

    // Pull out both the message‐type (ADT vs SIU etc) and the event code
    let mapping;
    if (msgType === "ADT") {
      if (!ADT_TYPE_STATUS_MAP.hasOwnProperty(eventCode)) 
        throw new Error(`Unsupported HL7 event code: ${eventCode}`);
      mapping = ADT_TYPE_STATUS_MAP[eventCode];
    } else {
      if (!SIU_TYPE_STATUS_MAP.hasOwnProperty(eventCode)) 
        throw new Error(`Unsupported HL7 event code: ${eventCode}`);
      mapping = SIU_TYPE_STATUS_MAP?.[eventCode];
    }

    // build patient / identifier / appointment blobs dynamically
    const patientData = await buildPayload(validated, spec.Patient, "Patient");
    patientData.updated_by = context.function.function_id;
    const identifierData = await buildPayload(
      validated,
      spec.PatientIdentifier,
      "PatientIdentifier"
    );
    const apptData = await buildPayload(
      validated,
      spec.Appointment,
      "Appointment"
    );

    const hl7MessageData = {
      mrn: identifierData.identifier_value,
      message_type: msgType,
      hdr: eventCode,
      message: eventInstance,
      processed_at: Date.now(),
      updated_by: context.function.function_id,

    }
    
    await Hl7Message.create(hl7MessageData, {
      transaction,
    });

    // upsert Patient + Identifier
    let identifier = await PatientIdentifier.findOne({
      where: { identifier_value: identifierData.identifier_value },
      transaction,
    });

    let patient;
    if (identifier) {
      patient = await Patient.findByPk(identifier.patient_id, {
        transaction,
      });
      await patient.update(patientData, { transaction });
    } else {
      patient = await Patient.create(patientData, { transaction });
      identifierData.patient_id = patient.patient_id;
      identifierData.effective_from = new Date();
      identifierData.updated_by = context.function.function_id;
      identifier = await PatientIdentifier.create(identifierData, {
        transaction,
      });
    }

    // upsert Appointment  by hl7_appointment_id
    apptData.patient_id = patient.patient_id;
    apptData.type = mapping.type;
    if (mapping.status) apptData.status = mapping.status;
    apptData.updated_by = context.function.function_id;
    // Create an event trace for further events that will be triggered by json rule engine
    const eventTraceInstance = await EventTrace.create(
      {
        function_id: context.function.function_id,
      },
      { transaction }
    );
    const traceContext = eventTraceInstance.get({ plain: true });

    let appt = await Appointment.findOne({
      where: {
        patient_id: apptData.patient_id,
        hl7_appointment_id: apptData.hl7_appointment_id,
      },
      transaction,
    });
    if (appt) {
      // update any changed fields (excluding the hl7_appointment_id itself)
      await appt.update(apptData, { transaction, traceContext });
    } else {
      // create new appointment record
      appt = await Appointment.create(apptData, { transaction, traceContext });
    }

    await transaction.commit();
    logger.info(`[TRACE ${traceId}] Completed HL7 processing`);
  } catch (err) {
    await transaction.rollback();
    logger.error(`[TRACE ${traceId}] Transaction rolled back: ${err.message}`);
    throw err;
  }
};

module.exports = hl7Processor;
