module.exports = (sequelize, DataTypes) => {
  const StagingData = sequelize.define(
    'StagingData',
    {
      staging_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      agent_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      record_hash: {
        type: DataTypes.STRING(64),
        allowNull: false,
      },
      identifier: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: 'Dynamic identifier field that stores the value of the staging_key from agent configuration',
      },
      original_data: {
        type: DataTypes.JSONB,
        allowNull: false,
      },
      processed_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      processing_status: {
        type: DataTypes.ENUM('pending', 'processed', 'failed'),
        defaultValue: 'pending',
        allowNull: false,
      },
      error_message: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      tableName: "staging_data",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          unique: true,
          fields: ['agent_id', 'identifier', 'record_hash'],
          name: 'staging_data_unique_record'
        },
        {
          fields: ['agent_id', 'identifier'],
          name: 'staging_data_agent_identifier'
        },
        {
          fields: ['processing_status'],
          name: 'staging_data_processing_status'
        }
      ]
    }
  );

  StagingData.associate = (models) => {
    StagingData.belongsTo(models.Agent, {
      foreignKey: "agent_id",
      as: "agent",
      onDelete: "CASCADE"
    });
  };

  return StagingData;
};
