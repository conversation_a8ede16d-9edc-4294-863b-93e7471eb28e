const { runRulesEngine, runBulkRulesEngine } = require("../../services/event.service");
const logger = require("../../config/logger");
const config = require("../../config/config");
const { Engine } = require("json-rules-engine");

/**
 * Trigger Plugin
 *
 * Adds lifecycle hooks to the model to trigger events based on dynamic rules.
 * Optimized for bulk operations with dependency analysis.
 *
 * @param {Object} model - The Sequelize model.
 * @param {Array<string>} triggers - Operations to trigger on.
 * @param {Object} rules - Static rules to pass to the rule generator.
 * @param {Object} options - Plugin options for bulk processing
 */
module.exports = (model, triggers = [], rules = [], options = {}) => {
  if (!config.messageQueuing.status) return;

  const rulesEngine = new Engine();
  let rawFacts = new Set();
  let ruleDependencyAnalysis = null;

  const validOperators = [
    "equal",
    "notEqual",
    "greaterThan",
    "lessThan",
    "in",
    "notIn",
  ];
  try {
    // Ensure rule is an array (if not, wrap it)
    const rulesToAdd = Array.isArray(rules) ? rules : [rules];

    rulesToAdd.forEach((rule) => {
      let facts = [];
      // Check if a top-level fact exists. If not, skip this check or iterate through conditions.all
      if (rule.fact) {
        facts.push(rule.fact);
      } else if (rule.conditions && rule.conditions.all) {
        // Extract facts from each condition
        rule.conditions.all.forEach((condition) => {
          if (condition.fact) facts.push(condition.fact);
        });
      }

      // Check if the fact exists in the model schema
      facts.forEach((fact) => {
        const field = fact.startsWith("changed.") ? fact.slice(8) : fact;
        if (
          (field !== "function" && field !== "application") && !Object.prototype.hasOwnProperty.call(model.rawAttributes, field)
        ) {
          logger.warn(
            `Field '${fact}' does not exist in schema; skipping rule '${rule.name}'.`
          );
          return;
        }
        rawFacts.add(fact);
      });

      // Validate operator for each condition if available
      let valid = true;
      if (rule.fact) {
        if (!validOperators.includes(rule.operator)) {
          logger.warn(
            `Operator '${rule.operator}' is not valid; skipping rule '${rule.name}'.`
          );
          valid = false;
        }
      } else if (rule.conditions && rule.conditions.all) {
        rule.conditions.all.forEach((condition) => {
          if (!validOperators.includes(condition.operator)) {
            logger.warn(
              `Operator '${condition.operator}' is not valid in rule '${rule.name}'; skipping rule.`
            );
            valid = false;
          }
        });
      }
      if (!valid) {
        logger.warn(`Skipping invalid rule: ${rule.name}`);
        return;
      }

      rulesEngine.addRule(rule);
    });

  } catch (err) {
    logger.error(`Error adding rule for ${model.name}:`, err);
  }

  if (triggers.includes("create")) {
    // Individual create hook - always use runRulesEngine
    model.addHook("afterCreate", async (instance, options) => {
      logger.info(`[HOOK] afterCreate triggered for ${model.name} - running rules engine`);
      await runRulesEngine({ instance, rulesEngine, rawFacts, traceContext: { ...options.traceContext, recorded: !!options.traceContext } });
    });

    // Bulk create hook - always use runBulkRulesEngine
    model.addHook("afterBulkCreate", async (instances, options) => {
      const instanceCount = instances.length;
      logger.info(`[HOOK] afterBulkCreate triggered for ${model.name} with ${instanceCount} instances`);

      // Use default configuration if not provided (simplified since we mimic individual operations)
      const effectiveRuleDependencyAnalysis = ruleDependencyAnalysis || {
        independentRules: [],
        dependentGroups: [],
        canBulkProcess: true
      };

      const functionId = options.functionId || global.APP_CONTEXT?.functionId;
      let performanceMonitor = null;
      if (options.performanceMonitor) {
        performanceMonitor = options.performanceMonitor;
      }
      await runBulkRulesEngine({
        instances,
        rulesEngine,
        rawFacts,
        ruleDependencyAnalysis: effectiveRuleDependencyAnalysis,
        functionId,
        modelName: model.name,
        performanceMonitor,
      });
    });
  }

  if (triggers.includes("update")) {
    // Individual update hook - always use runRulesEngine
    model.addHook("afterUpdate", async (instance, options) => {
      logger.info(`[HOOK] afterUpdate triggered for ${model.name} - running rules engine`);
      await runRulesEngine({ instance, rulesEngine, rawFacts, traceContext: { ...options.traceContext, recorded: !!options.traceContext } });
    });

    // Bulk update hook - always use runBulkRulesEngine
    model.addHook("afterBulkUpdate", async (options) => {
      logger.info(`[HOOK] afterBulkUpdate triggered for ${model.name} - fetching updated instances`);

      // For bulk updates, we need to fetch the updated instances to run rules on them
      if (options.where) {
        const updatedInstances = await model.findAll({ where: options.where });
        const instanceCount = updatedInstances.length;
        logger.info(`[HOOK] Found ${instanceCount} updated instances for rules engine`);

        // Use default configuration if not provided (simplified since we mimic individual operations)
        const effectiveRuleDependencyAnalysis = ruleDependencyAnalysis || {
          independentRules: [],
          dependentGroups: [],
          canBulkProcess: true
        };

        const functionId = options.functionId || global.APP_CONTEXT?.functionId;
        let performanceMonitor = null;
        if (options.performanceMonitor) {
          performanceMonitor = options.performanceMonitor;
        }
        await runBulkRulesEngine({
          instances: updatedInstances,
          rulesEngine,
          rawFacts,
          ruleDependencyAnalysis: effectiveRuleDependencyAnalysis,
          functionId,
          modelName: model.name,
          performanceMonitor
        });
      }
    });
  }

  if (triggers.includes("delete")) {
    // Individual delete hook - always use runRulesEngine
    model.addHook("beforeDestroy", async (instance, options) => {
      logger.info(`[HOOK] beforeDestroy triggered for ${model.name} - running rules engine`);
      await runRulesEngine({ instance, rulesEngine, rawFacts, traceContext: { ...options.traceContext, recorded: !!options.traceContext } });
    });
  }
};
