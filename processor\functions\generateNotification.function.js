const logger = require("../config/logger");
const { getCachedEventConfig, getCachedNotificationConfig } = require("../utils/caching");
const { sendToRabbitMQ } = require("../services/event.service");

/**
 * Adds default guests for a given appointment event.
 *
 * @param {Object} event - The event instance containing appointment reference.
 * @param {string|number} event.parent_id - ID of the appointment to add guests for.
 * @param {Object} context - Execution context with tracing information.
 * @param {string} context.trace_id - Unique trace identifier for logging.
 * @returns {Promise<void>} Resolves when guests have been added, or rejects on error.
 * @throws {Error} If the appointment is not found or any database operation fails.
 */
const generateNotification = async (event, context) => {
    const traceId = context.trace_id;
    logger.info(`[TRACE ${traceId}] Starting add default guests of patient`);

    try {
        const notification_name = event.params.notification;
        const notificationConfigArray = await getCachedNotificationConfig(notification_name);

        if (!notificationConfigArray || notificationConfigArray.length === 0) {
            throw `No configuration found for notification named as: ${notification_name}`;
        }

        // Iterate over the notification config array
        for (const notificationConfig of notificationConfigArray) {
            const { channel } = notificationConfig;
            let { queue, order } = await getCachedEventConfig(channel)
            event.event_type = channel;
            event.queue = queue;
            event.order = order;
            event.notification = notificationConfig;
            await sendToRabbitMQ(event);
            logger.info(`[TRACE ${traceId}] Notification, '${notification_name}' generated for channel: ${channel}`);
        }

    } catch (error) {
        logger.error(`[TRACE ${traceId}] Error generating notification: ${error.message}`);
        throw error;
    }
};

module.exports = generateNotification;
