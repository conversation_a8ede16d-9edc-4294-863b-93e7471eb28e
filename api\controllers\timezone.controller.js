const { Timezone, Country } = require("../models");
const { sendSuccess, sendError } = require("../utils/ApiResponse");
const { status: httpStatus } = require("http-status");
const catchAsync = require("../utils/catchAsync");

/**
 * @class TimezoneController
 * @description Controller for managing timezones
 */
const TimezoneController = {
  index: catchAsync(async (req, res) => {
    const timezones = await Timezone.findAll({ include: { model: Country, as: "country" } });
    sendSuccess(res, "Timezones retrieved successfully", httpStatus.OK, timezones);
  }),

  show: catchAsync(async (req, res) => {
    const { timeId } = req.params;
    const timezone = await Timezone.findByPk(timeId, { include: { model: Country, as: "country" } });
    if (!timezone) return sendError(res, "Timezone not found", httpStatus.NOT_FOUND);
    sendSuccess(res, "Timezone retrieved successfully", httpStatus.OK, timezone);
  }),

  create: catchAsync(async (req, res) => {
    const timezone = await Timezone.create(req.body);
    sendSuccess(res, "Timezone created successfully", httpStatus.CREATED, timezone);
  }),

  update: catchAsync(async (req, res) => {
    const { timeId } = req.params;
    const [updated] = await Timezone.update(req.body, { where: { time_id: timeId } });
    if (!updated) return sendError(res, "Failed to update timezone", httpStatus.BAD_REQUEST);
    sendSuccess(res, "Timezone updated successfully", httpStatus.OK);
  }),

  delete: catchAsync(async (req, res) => {
    const { timeId } = req.params;
    const deleted = await Timezone.destroy({ where: { time_id: timeId } });
    if (!deleted) return sendError(res, "Failed to delete timezone", httpStatus.BAD_REQUEST);
    sendSuccess(res, "Timezone deleted successfully", httpStatus.NO_CONTENT);
  }),
};

module.exports = TimezoneController;