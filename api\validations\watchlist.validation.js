const Joi = require("joi");
const { getModelAttributes } = require("../utils/helpers");
const { Watchlist , WatchlistHistoryView } = require("../models");
const { existsMasterData } = require("./custom.validation");


const watchlistAttributes = getModelAttributes(Watchlist);
const watchlistHistoryAttributes = getModelAttributes(WatchlistHistoryView);


module.exports = {
  createWatchlistValidation: {
    body: Joi.object().keys({
      first_name: Joi.string().required(),
      middle_name: Joi.string().optional().allow(""),
      last_name: Joi.string().required(),
      suffix: Joi.string().optional().allow(""),
      date_of_birth: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      email: Joi.string().email().optional().allow(""),
      phone: Joi.string().optional().allow(""),
      host: Joi.string().optional().allow(""),
      address: Joi.string().optional().allow(""),
      expiry_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      reason: Joi.number().integer().external(existsMasterData("watchlist_reason")).optional(),
      description: Joi.string().optional().allow(""),
      status: Joi.number().integer().external(existsMasterData("watchlist_status")).optional(),
       image: Joi.string().uri().optional(),
    }),
  },
  getWatchlistValidation: {
    params: Joi.object().keys({
      id: Joi.string().uuid().required(),
    }),
  },
  getWatchlist: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().valid(...watchlistAttributes).optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      search: Joi.string().allow("").optional(),
    }),
  },
  getWatchlistHistoryValidation: {
    params: Joi.object().keys({
      watchlist_id: Joi.string().uuid().required(),
    }),
  },
  getAllWatchlistHistory: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().valid(...watchlistHistoryAttributes).optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
    }),
  },

  getWatchlistDetailsValidation: {
    query: Joi.object().keys({
      watchlist_id: Joi.string().uuid().required(),
    }),
  },
  updateWatchlistValidation: {
    params: Joi.object().keys({
      id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      first_name: Joi.string().optional(),
      middle_name: Joi.string().optional().allow(""),
      last_name: Joi.string().optional(),
      suffix: Joi.string().optional().allow(""),
      date_of_birth: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      email: Joi.string().email().optional(),
      phone: Joi.string().optional(),
      host: Joi.string().optional(),
      address: Joi.string().optional(),
      expiry_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      status: Joi.number().integer().external(existsMasterData("watchlist_status")).optional(),
    }),
  },
  updateWatchlistImage: {
    params: Joi.object().keys({
      watchlist_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      image: Joi.string().uri().required(),
    }),
  },
  deleteWatchlistValidation: {
    params: Joi.object().keys({
      id: Joi.string().uuid().required(),
    }),
  },

  // Validation for creating watchlist document
  createWatchlistDocument: {
    params: Joi.object().keys({
      watchlist_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      name: Joi.string().required(),
      image: Joi.string().required(), // Base64 encoded image or document
    }),
  },

  // Validation for updating watchlist reason
  updateWatchlistReason: {
    params: Joi.object().keys({
      watchlist_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      reason: Joi.string().optional().allow(""),
      description: Joi.string().optional().allow(""),
    }),
  },

  // Validation for getting watchlist document
  getWatchlistDocument: {
    params: Joi.object().keys({
      watchlist_id: Joi.string().uuid().required(),
    }),
  },

  // Validation for deleting watchlist document
  deleteWatchlistDocument: {
    params: Joi.object().keys({
      watchlist_document_id: Joi.string().uuid().required(),
    }),
  },
};
