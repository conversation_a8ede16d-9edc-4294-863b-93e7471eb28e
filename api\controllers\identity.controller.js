const {
  Identity,
  Card,
  IdentityAccess,
  Facility,
  Address,
  Country,
  State,
  IdentityAccessView,
  AccessLevel,
} = require("../models");
const catchAsync = require("../utils/catchAsync");
const { sendSuccess, sendError } = require("../utils/ApiResponse");
const { paginate } = require("../utils/plugins/paginate");
const httpStatus = require("http-status");
const { Op, where } = require("sequelize");
const { formatIdentity } = require("../utils/helpers");
const { v4: uuidValidate } = require("uuid");
const Joi = require("joi");
const { up } = require("../migrations/create-view-identity-access");
const _ = require("lodash");

// CRUD for Identity
exports.createIdentity = catchAsync(async (req, res) => {
  // Clean up date fields - convert empty strings to null
  const cleanedData = { ...req.body };
  const dateFields = ['start_date', 'end_date', 'suspension_date'];

  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });

  const identity = await Identity.create(cleanedData);
  sendSuccess(
    res,
    "Identity created successfully",
    httpStatus.CREATED,
    identity
  );
});

exports.getIdentity = catchAsync(async (req, res) => {
  const { page, limit, sortBy, sortOrder, status, name, eid, type } = req.query;
  const where = {};
  if (status) where.status = status;
  if (name)
    where[Op.or] = [
      { first_name: { [Op.iLike]: `%${name}%` } },
      { last_name: { [Op.iLike]: `%${name}%` } },
    ];
  if (eid) where.eid = eid;
  if (type) where.identity_type = type;
  const identities = await paginate(
    Identity,
    { where },
    { page, limit, sortBy, sortOrder }
  );
  sendSuccess(
    res,
    "Identity retrieved successfully",
    httpStatus.OK,
    identities
  );
});
exports.getIdentityEid = catchAsync(async (req, res) => {
  const identities = await Identity.findAll({
    attributes: ["eid"], // Include only the `eid` attribute
  });
  if (!identities || identities.length === 0) {
    return sendError(res, "No identities found", httpStatus.NOT_FOUND);
  }
  const formattedIdentities = await Promise.all(
    identities.map(async (identity) => {
      return formatIdentity(identity.identity_id); // Assuming identity_id is accessible
    })
  );
  sendSuccess(
    res,
    "Identities retrieved successfully",
    httpStatus.OK,
    formattedIdentities
  );
});

exports.getIdentityHub = catchAsync(async (req, res) => {
  const { page, limit, sortBy, sortOrder, status, name, eid, type, search } =
    req.query;
  const where = {};
  if (status !== undefined) where.status = status;
  if (name) {
    where[Op.or] = [
      { first_name: { [Op.iLike]: `%{name}%` } },
      { last_name: { [Op.iLike]: `%{name}%` } },
    ];
  }
  if (eid) where.eid = eid;
  if (type) where.identity_type = type;
  if (search) {
    where[Op.or] = [
      { first_name: { [Op.iLike]: `%${search}%` } },
      { last_name: { [Op.iLike]: `%${search}%` } },
      { eid: { [Op.iLike]: `%${search}%` } },
      { job_title: { [Op.iLike]: `%${search}%` } },
      { company: { [Op.iLike]: `%${search}%` } },
      { organization: { [Op.iLike]: `%${search}%` } },
    ];
  }
  const identities = await paginate(
    Identity,
    { where },
    {
      page,
      limit,
      sortBy,
      sortOrder,
      attributes: [
        "first_name",
        "last_name",
        "eid",
        "identity_type",
        "company",
        "organization",
        "job_title",
        "end_date",
        "status",
      ],
    }
  );
  sendSuccess(
    res,
    "Identity retrieved successfully",
    httpStatus.OK,
    identities
  );
});

exports.getIdentityAccessList = catchAsync(async (req, res) => {
  const {
    page,
    limit,
    sortBy = "identity_access_id", // 👈 Set default sort column that exists
    sortOrder = "DESC", // 👈 Optional default sort order
    status,
    area_name,
    eid,
    pacs_area_name,
    system,
  } = req.query;

  const where = {};
  if (status) where.status = status;
  if (area_name) where.access_area = { [Op.iLike]: `%${area_name}%` };
  if (eid) where.eid = eid;
  if (pacs_area_name)
    where.pacs_area_name = { [Op.iLike]: `%{pacs_area_name}%` };
  if (system) where.system_name = { [Op.iLike]: `%${system}%` };

  const identityAccess = await paginate(
    IdentityAccessView,
    { where },
    { page, limit, sortBy, sortOrder } // 👈 use custom sorting to avoid default
  );

  sendSuccess(
    res,
    "Identity Access retrieved successfully",
    httpStatus.OK,
    identityAccess
  );
});

exports.getOrganization = catchAsync(async (req, res) => {
  const { identity_id } = req.query; // Assuming you want to filter by eid
  const where = {};
  if (identity_id) {
    where.identity_id = identity_id; // Filter by eid if provided
  }
  const identities = await Identity.findAll({
    where,
    attributes: [
      "first_name",
      "last_name",
      "eid",
      "company",
      "company_code",
      "organization",
      "job_title",
      "job_code",
      "manager",
      "status",
    ],
    include: [
      {
        model: Facility, // Reference to the Facility model
        as: "facility",
        attributes: [
          "facility_id",
          "name", // or 'facility_name' if you want the name field
        ],
        include: [
          {
            model: Address, // Reference to the Address model
            as: "address",
            attributes: [
              "address_line_1",
              "address_line_2",
              "postal_code",
              "region", // Assuming you want to include region
              "country_id", // If you want to include country_id, otherwise omit
              "state_id", // If you want to include state_id, otherwise omit
            ],

            include: [
              {
                model: Country,
                as: "country",
                attributes: ["name"],
              },
              {
                model: State,
                as: "state",
                attributes: ["name"],
              },
            ],
          },
        ],
      },
    ],
  });
  sendSuccess(
    res,
    "Organization details retrieved successfully",
    httpStatus.OK,
    identities
  );
});

exports.updateOrganization = catchAsync(async (req, res) => {
  const { identity_id, manager, ...body } = req.body;

  // Validate identity_id format
  if (!uuidValidate(identity_id)) {
    return sendError(res, "Invalid identity_id format", httpStatus.BAD_REQUEST);
  }

  // Validate manager UUID if provided
  if (manager && !uuidValidate(manager)) {
    return sendError(
      res,
      "Invalid manager UUID format",
      httpStatus.BAD_REQUEST
    );
  }

  // Configure allowed fields with proper validation
  const allowedFields = {
    first_name: Joi.string().optional(),
    last_name: Joi.string().optional(),
    eid: Joi.string().optional(),
    company: Joi.string().optional(),
    company_code: Joi.string().optional(),
    organization: Joi.string().optional(),
    job_title: Joi.string().optional(),
    job_code: Joi.string().optional(),
    manager: Joi.string().guid().optional(),
    status: Joi.number().integer().optional(),
    updated_by: Joi.string().guid().optional(),
  };

  // Validate request body
  const { error } = Joi.object(allowedFields).validate(body);
  if (error) {
    return sendError(res, error.message, httpStatus.BAD_REQUEST);
  }

  // Prepare update data
  const updateData = _.pick(body, Object.keys(allowedFields));

  // Perform update
  const [updatedCount] = await Identity.update(updateData, {
    where: { identity_id },
    returning: true,
    individualHooks: true,
  });

  if (updatedCount === 0) {
    return sendError(res, "Identity not found", httpStatus.NOT_FOUND);
  }

  // Return updated identity
  const updatedIdentity = await Identity.findByPk(identity_id, {
    attributes: Object.keys(allowedFields),
  });

  sendSuccess(
    res,
    "Organization updated successfully",
    httpStatus.OK,
    updatedIdentity
  );
});

exports.getIdentityById = catchAsync(async (req, res) => {
  const { identity_id } = req.params;
  const identity = await Identity.findByPk(identity_id);
  sendSuccess(res, "Identity retrieved successfully", httpStatus.OK, identity);
});
exports.basicIdentity = catchAsync(async (req, res) => {
  const { identity_id } = req.params;
  const identity = await Identity.findByPk(identity_id);
  const {
    first_name,
    last_name,
    image,
    identity_type,
    start_date,
    end_date,
    status,
    job_title,
  } = identity;
  const responseData = {
    first_name,
    last_name,
    image,
    identity_type,
    job_title,
    start_date,
    end_date,
    status,
  };
  sendSuccess(
    res,
    "Identity retrieved successfully",
    httpStatus.OK,
    responseData
  );
});

exports.updateIdentity = catchAsync(async (req, res) => {
  const { identity_id } = req.params;
  const identity = await Identity.findByPk(identity_id);

  const cleanedData = { ...req.body };
  const dateFields = ['start_date', 'end_date', 'suspension_date'];
  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });
  await identity.update(cleanedData);
  sendSuccess(res, "Identity updated successfully", httpStatus.OK, identity);
});

exports.deleteIdentity = catchAsync(async (req, res) => {
  const { identity_id } = req.params;
  const identity = await Identity.findByPk(identity_id);
  if (!identity)
    return sendError(res, "Identity not found", httpStatus.NOT_FOUND);
  await identity.destroy();
  sendSuccess(res, "Identity deleted successfully", httpStatus.NO_CONTENT);
});

// CRUD for Card
exports.createCard = catchAsync(async (req, res) => {
  // Clean up date fields - convert empty strings to null
  const cleanedData = { ...req.body };
  const dateFields = ['active_date', 'deactive_date'];

  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });

  const card = await Card.create(cleanedData);
  sendSuccess(res, "Card created successfully", httpStatus.CREATED, card);
});

exports.getCardbyId = catchAsync(async (req, res) => {
  const { card_id } = req.params;
  const card = await Card.findByPk(card_id);
  if (!card) return sendError(res, "Card not found", httpStatus.NOT_FOUND);
  sendSuccess(res, "Card retrieved successfully", httpStatus.OK, card);
});
exports.getCardbyIdentity = catchAsync(async (req, res) => {
  const { identity_id } = req.params;
  const {
    page = 1,
    limit = 10,
    search,
    sortBy = "card_number",
    sortOrder = "ASC",
  } = req.query;
  const where = { identity_id };

  if (search) {
    const searchConditions = [{ card_number: { [Op.iLike]: `%${search}%` } }];
    if (!isNaN(search)) {
      searchConditions.push({ status: parseInt(search, 10) });
    }
    if (!isNaN(search)) {
      searchConditions.push({ template: parseInt(search, 10) });
    }
  }
  const result = await paginate(
    Card,
    { where },
    { page, limit, sortBy, sortOrder }
  );
  sendSuccess(res, "Card(s) retrieved successfully", httpStatus.OK, result);
});

exports.getCards = catchAsync(async (req, res) => {
  const { page, limit, sortBy, sortOrder, status, card_number, card_format } =
    req.query;
  const where = {};
  if (status) where.status = status;
  if (card_number) where.card_number = { [Op.iLike]: `%${card_number}%` };
  // if (eid) where.eid = eid;
  if (card_format) where.card_format = card_format;

  const cards = await paginate(
    Card,
    { where },
    { page, limit, sortBy, sortOrder }
  );
  sendSuccess(res, "Cards retrieved successfully", httpStatus.OK, cards);
});
exports.getCardList = catchAsync(async (req, res) => {
  const {
    page,
    limit,
    sortBy,
    sortOrder,
    status,
    card_number,
    card_format,
    search,
  } = req.query;
  const where = {};
  if (status) where.status = status;
  if (card_number) where.card_number = { [Op.iLike]: `%${card_number}%` };
  if (card_format) where.card_format = card_format;
  if (search) {
    const searchConditions = [{ card_number: { [Op.iLike]: `%${search}%` } }];
    if (!isNaN(search)) {
      searchConditions.push({ status: parseInt(search, 10) });
    }
    if (!isNaN(search)) {
      searchConditions.push({ template: parseInt(search, 10) });
    }

    where[Op.or] = searchConditions;
  }

  const attributes = [
    "card_number",
    "template",
    "created_by",
    "deactive_date",
    "status",
  ];
  const cards = await paginate(
    Card,
    { where, attributes },
    { page, limit, sortBy, sortOrder }
  );
  // Format the `created_by` attribute
  const formattedCards = await Promise.all(
    cards.data.map(async (card) => {
      if (card.created_by) {
        card.created_by = await formatIdentity(card.created_by);
      }
      return card;
    })
  );

  sendSuccess(res, "Cards retrieved successfully", httpStatus.OK, {
    ...cards,
    data: formattedCards,
  });
});

exports.updateCard = catchAsync(async (req, res) => {
  const { card_id } = req.params;
  const card = await Card.findByPk(card_id);
  if (!card) return sendError(res, "Card not found", httpStatus.NOT_FOUND);

  // Clean up date fields - convert empty strings to null
  const cleanedData = { ...req.body };
  const dateFields = ['active_date', 'deactive_date'];

  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });

  await card.update(cleanedData);
  sendSuccess(res, "Card updated successfully", httpStatus.OK, card);
});

exports.deleteCard = catchAsync(async (req, res) => {
  const { card_id } = req.params;
  const card = await Card.findByPk(card_id);
  if (!card) return sendError(res, "Card not found", httpStatus.NOT_FOUND);
  await card.destroy();
  sendSuccess(res, "Card deleted successfully", httpStatus.NO_CONTENT);
});

// CRUD for IdentityAccess
exports.createIdentityAccess = catchAsync(async (req, res) => {
  const identityAccess = await IdentityAccess.create(req.body);
  sendSuccess(
    res,
    "IdentityAccess created successfully",
    httpStatus.CREATED,
    identityAccess
  );
});

exports.getIdentityAccessById = catchAsync(async (req, res) => {
  const { identity_access_id } = req.params;

  // Fetch identity access along with the associated access level
  const identityAccess = await IdentityAccess.findByPk(identity_access_id, {
    include: [
      {
        model: AccessLevel, // Assuming you have an AccessLevel model defined
        as: "accessLevel", // This should match the alias used in your associations
      },
    ],
  });
  // Extract the access level name
  const accessLevelName = identityAccess.accessLevel
    ? identityAccess.accessLevel.name
    : null;
  // Send success response with access level name
  sendSuccess(res, "IdentityAccess retrieved successfully", httpStatus.OK, {
    identityAccess,
    accessLevelName,
  });
});
exports.getIdentityAccessByIdentity = catchAsync(async (req, res) => {
  const { identity_id } = req.params;
  const {
    page = 1,
    limit = 10,
    search,
    sortBy = "start_date",
    sortOrder = "DESC",
  } = req.query;

  const where = { identity_id };

  if (search) {
    // Check if search term is a valid number for status
    const numericSearch = Number(search);
    const isNumeric = !isNaN(numericSearch);

    where[Op.or] = [
      { "$access_level.name$": { [Op.iLike]: `%${search}%` } },
      { "$card.card_number$": { [Op.iLike]: `%${search}%` } },
      // Handle numeric status search
      ...(isNumeric ? [{ status: numericSearch }] : []),
    ];
  }

  const result = await paginate(
    IdentityAccess,
    {
      where,
      attributes: ["start_date", "end_date", "status", "identity_access_id"],
      include: [
        {
          model: AccessLevel,
          as: "access_level",
          attributes: ["name"],
          required: false,
        },
        {
          model: Card,
          as: "card",
          attributes: ["card_number"],
          required: false,
        },
      ],
      distinct: true,
    },
    { page, limit, sortBy, sortOrder }
  );

  if (!result.data || result.data.length === 0) {
    return sendError(
      res,
      "No Identity Access records found",
      httpStatus.NOT_FOUND
    );
  }

  const accessDetails = result.data.map((entry) => ({
    identity_access_id: entry.identity_access_id,
    access_level_name: entry.access_level?.name || null,
    card_number: entry.card?.card_number || null,
    start_date: entry.start_date,
    end_date: entry.end_date,
    status: entry.status,
  }));

  return sendSuccess(
    res,
    "Identity Access details retrieved successfully",
    httpStatus.OK,
    { ...result, data: accessDetails }
  );
});

exports.getIdentityAccess = catchAsync(async (req, res) => {
  const {
    page,
    limit,
    sortBy,
    sortOrder,
    status,
    area_name,
    eid,
    pacs_area_name,
    system,
    search,
  } = req.query;

  const where = {};
  if (status) where.status = status;
  if (area_name) where.access_area = { [Op.iLike]: `%${area_name}%` };
  if (eid) where.eid = eid;
  if (pacs_area_name)
    where.pacs_area_name = { [Op.iLike]: `%${pacs_area_name}%` };
  if (system) where.system = { [Op.iLike]: `%${system}%` };
  if (search) {
    where[Op.or] = [
      { access_area: { [Op.iLike]: `%${search}%` } },
      { pacs_area_name: { [Op.iLike]: `%${search}%` } },
      { eid: { [Op.iLike]: `%${search}%` } },
      { system: { [Op.iLike]: `%${search}%` } },
    ];
  }

  const identityAccess = await paginate(
    IdentityAccess,
    { where },
    { page, limit, sortBy, sortOrder }
  );

  if (!identityAccess.data || identityAccess.data.length === 0) {
    return sendError(res, "IdentityAccess not found", httpStatus.NOT_FOUND);
  }

  sendSuccess(
    res,
    "IdentityAccess retrieved successfully",
    httpStatus.OK,
    identityAccess
  );
});

exports.getIdentityAccessDetails = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    search,
    sortBy = "start_date",
    sortOrder = "DESC",
    status,
  } = req.query;

  const where = {};
  if (status !== undefined) {
    where.status = status;
  }
  if (search) {
    const searchTerm = `%${search}%`;
    where[Op.or] = [
      { "$access_level.name$": { [Op.iLike]: searchTerm } },
      { "$card.card_number$": { [Op.iLike]: searchTerm } },
    ];
  }

  const identityAccess = await IdentityAccess.findAll({
    attributes: ["start_date", "end_date", "status", "created_by"],
    include: [
      {
        model: AccessLevel,
        as: "access_level",
        attributes: ["name"],
      },
      {
        model: Card,
        as: "card",
        attributes: ["card_number"],
      },
    ],
    where,
    limit,
    offset: (page - 1) * limit,
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
  });

  const accessDetails = await Promise.all(
    identityAccess.map(async (entry) => {
      const createdByFormatted = await formatIdentity(entry.created_by);
      return {
        access_level_name: entry.access_level.name,
        card_number: entry.card.card_number,
        start_date: entry.start_date,
        end_date: entry.end_date,
        status: entry.status,
        created_by: createdByFormatted,
      };
    })
  );
  return sendSuccess(
    res,
    "Identity Access details retrieved successfully",
    httpStatus.OK,
    accessDetails
  );
});

exports.updateIdentityAccess = catchAsync(async (req, res) => {
  const { identity_access_id } = req.params;
  const identityAccess = await IdentityAccess.findByPk(identity_access_id);
  if (!identityAccess)
    return sendError(res, "IdentityAccess not found", httpStatus.NOT_FOUND);
  await identityAccess.update(req.body);
  sendSuccess(
    res,
    "IdentityAccess updated successfully",
    httpStatus.OK,
    identityAccess
  );
});

exports.deleteIdentityAccess = catchAsync(async (req, res) => {
  const { identity_access_id } = req.params;
  const identityAccess = await IdentityAccess.findByPk(identity_access_id);
  if (!identityAccess)
    return sendError(res, "IdentityAccess not found", httpStatus.NOT_FOUND);
  await identityAccess.destroy();
  sendSuccess(
    res,
    "IdentityAccess deleted successfully",
    httpStatus.NO_CONTENT
  );
});

exports.getAccessLevelName = catchAsync(async (req, res) => {
  const { search } = req.query;
  const whereClause = {};
  if (search) {
    whereClause.name = {
      [Op.iLike]: `%${search}%`,
    };
  }
  const accessLevels = await AccessLevel.findAll({
    where: whereClause, // Apply the where clause
    attributes: ["access_level_id", "name"], // Retrieve both access_level_id and name
  });
  if (!accessLevels || accessLevels.length === 0) {
    return sendError(res, "No Access Levels found", httpStatus.NOT_FOUND);
  }
  // Map to an array of objects containing both id and name
  const accessLevelInfo = accessLevels.map((level) => ({
    id: level.access_level_id,
    name: level.name,
  }));
  return sendSuccess(
    res,
    "Access Levels retrieved successfully",
    httpStatus.OK,
    accessLevelInfo
  );
});

exports.getCardNumber = catchAsync(async (req, res) => {
  const cards = await Card.findAll({
    attributes: ["card_id", "card_number"], // Retrieve both id and card_number
  });
  if (!cards || cards.length === 0) {
    return sendError(res, "No Card Numbers found", httpStatus.NOT_FOUND);
  }
  // Map to an array of objects containing both id and card_number
  const cardInfo = cards.map((card) => ({
    id: card.card_id, // Assuming 'id' is the primary key or identifier for the Card model
    card_number: card.card_number,
  }));
  return sendSuccess(
    res,
    "Card numbers retrieved successfully",
    httpStatus.OK,
    cardInfo
  );
});

exports.updateFacility = catchAsync(async (req, res) => {
  const { facility_id, identity_id } = req.body;
  if (!facility_id || !identity_id) {
    return sendError(
      res,
      "facility_id and identity_id are required",
      httpStatus.BAD_REQUEST
    );
  }
  const [updatedCount] = await Identity.update(
    { facility_id },
    { where: { identity_id } }
  );

  if (updatedCount === 0) {
    return sendError(res, "Identity not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "facility_id updated for identity", httpStatus.OK, {
    facility_id,
    identity_id,
  });
});
