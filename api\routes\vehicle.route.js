const express = require("express");
const auth = require("../middlewares/auth");
const validate = require("../middlewares/validate");
const VehicleValidation = require("../validations/vehicle.validation");
const VehicleController = require("../controllers/vehicle.controller");
const catchAsync = require("../utils/catchAsync");

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Vehicle:
 *       type: object
 *       required:
 *         - plate_number
 *         - identity_id
 *       properties:
 *         vehicle_id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the vehicle
 *         plate_number:
 *           type: string
 *           description: Vehicle license plate number
 *         issued_by:
 *           type: string
 *           description: Authority that issued the plate
 *         vin:
 *           type: string
 *           maxLength: 17
 *           description: Vehicle Identification Number
 *         year:
 *           type: integer
 *           minimum: 1900
 *           description: Manufacturing year
 *         make:
 *           type: string
 *           description: Vehicle manufacturer
 *         model:
 *           type: string
 *           description: Vehicle model
 *         color:
 *           type: string
 *           description: Vehicle color
 *         uploaded_date:
 *           type: string
 *           format: date
 *           description: Date when vehicle information was uploaded
 *         identity_id:
 *           type: string
 *           format: uuid
 *           description: Reference to identity
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /vehicles:
 *   post:
 *     summary: Create a new vehicle
 *     tags: [Vehicles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - plate_number
 *               - identity_id
 *             properties:
 *               plate_number:
 *                 type: string
 *                 example: "ABC-1234"
 *               issued_by:
 *                 type: string
 *                 example: "Department of Motor Vehicles"
 *               vin:
 *                 type: string
 *                 maxLength: 17
 *                 example: "1HGBH41JXMN109186"
 *               year:
 *                 type: integer
 *                 minimum: 1900
 *                 example: 2020
 *               make:
 *                 type: string
 *                 example: "Toyota"
 *               model:
 *                 type: string
 *                 example: "Camry"
 *               color:
 *                 type: string
 *                 example: "Blue"
 *               uploaded_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-15"
 *               identity_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       201:
 *         description: Vehicle created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/",
  auth("create_vehicle"),
  validate(VehicleValidation.createVehicle),
  catchAsync(VehicleController.createVehicle)
);

/**
 * @swagger
 * /vehicles:
 *   get:
 *     summary: Get all vehicles with pagination and filtering
 *     tags: [Vehicles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: identity_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by identity ID
 *       - in: query
 *         name: make
 *         schema:
 *           type: string
 *         description: Filter by vehicle make
 *       - in: query
 *         name: model
 *         schema:
 *           type: string
 *         description: Filter by vehicle model
 *       - in: query
 *         name: year
 *         schema:
 *           type: integer
 *         description: Filter by year
 *     responses:
 *       200:
 *         description: Vehicles retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/",
  auth("view_vehicle"),
  validate(VehicleValidation.getVehicles),
  catchAsync(VehicleController.getVehicles)
);

/**
 * @swagger
 * /vehicles/{vehicle_id}:
 *   get:
 *     summary: Get a vehicle by ID
 *     tags: [Vehicles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: vehicle_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Vehicle ID
 *     responses:
 *       200:
 *         description: Vehicle retrieved successfully
 *       404:
 *         description: Vehicle not found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/:vehicle_id",
  auth("view_vehicle"),
  validate(VehicleValidation.getVehicleById),
  catchAsync(VehicleController.getVehicleById)
);

/**
 * @swagger
 * /vehicles/{vehicle_id}:
 *   put:
 *     summary: Update a vehicle by ID
 *     tags: [Vehicles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: vehicle_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Vehicle ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               plate_number:
 *                 type: string
 *               issued_by:
 *                 type: string
 *               vin:
 *                 type: string
 *                 maxLength: 17
 *               year:
 *                 type: integer
 *                 minimum: 1900
 *               make:
 *                 type: string
 *               model:
 *                 type: string
 *               color:
 *                 type: string
 *               uploaded_date:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Vehicle updated successfully
 *       404:
 *         description: Vehicle not found
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.put(
  "/:vehicle_id",
  auth("edit_vehicle"),
  validate(VehicleValidation.updateVehicle),
  catchAsync(VehicleController.updateVehicle)
);

/**
 * @swagger
 * /vehicles/{vehicle_id}:
 *   delete:
 *     summary: Delete a vehicle by ID
 *     tags: [Vehicles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: vehicle_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Vehicle ID
 *     responses:
 *       200:
 *         description: Vehicle deleted successfully
 *       404:
 *         description: Vehicle not found
 *       401:
 *         description: Unauthorized
 */
router.delete(
  "/:vehicle_id",
  auth("delete_vehicle"),
  validate(VehicleValidation.deleteVehicle),
  catchAsync(VehicleController.deleteVehicle)
);



module.exports = router;
