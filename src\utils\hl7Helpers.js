const models = require("../models");
const { DataTypes } = require("sequelize");
const { getCachedMasterDataValue } = require("../utils/caching");

/**
 * Parse an HL7 date or datetime field:
 *  - YYYYMMDD           → Date at midnight
 *  - YYYYMMDDhhmmss     → Date with time
 */
function parseHL7Date(str) {
  const year = +str.slice(0, 4);
  const month = +str.slice(4, 6) - 1;
  const day = +str.slice(6, 8);

  // If no time part, just use midnight of that day
  if (str.length === 8) {
    return new Date(year, month, day);
  }

  // If there's a full hhmmss, parse those too
  const hour = +str.slice(8, 10);
  const minute = +str.slice(10, 12);
  const second = +str.slice(12, 14);

  return new Date(year, month, day, hour, minute, second);
}

// Map model‐name strings to actual Sequelize model classes
// Helper: check key is of type date or not
function isDateType(dataType) {
  // In most Sequelize versions, key lives on the constructor:
  const key =
    dataType.key || (dataType.constructor && dataType.constructor.key);
  return (
    key === DataTypes.DATE.key || // “DATE”
    key === DataTypes.DATEONLY.key
  ); // “DATEONLY”
}

// Build a “spec” by reading your JSON + looking at each field’s DataType
function buildSpec(mapping) {
  const spec = {};

  for (let [hl7Key, mapVal] of Object.entries(mapping)) {
    if (!mapVal) continue;
    const [modelName, fieldName] = mapVal.split(".");
    const model = models[modelName];
    if (!model) throw new Error(`Unknown model "${modelName}"`);

    spec[modelName] = spec[modelName] || {};
    const entry = (spec[modelName][fieldName] ||= {
      keys: [],
      parser: null,
      group: null,
    });
    entry.keys.push(hl7Key);

    const attr = model.rawAttributes[fieldName];
    if (!entry.parser && attr && isDateType(attr.type)) {
      entry.parser = parseHL7Date;
    }

    // Check if this field is a master data linked field
    const masterAssociation = Object.values(model.associations).find(
      (assoc) =>
        assoc.foreignKey === fieldName && assoc.scope && assoc.scope.group
    );

    if (masterAssociation) {
      entry.group = masterAssociation.scope.group; // Attach group name
    }
  }

  return spec;
}

// Generic builder that uses your spec to turn cleaned HL7 → payload
async function buildPayload(cleaned, fieldSpec, modelName) {
  const model = models[modelName];
  const payload = {};

  for (const [field, { keys, parser, group }] of Object.entries(fieldSpec)) {
    const vals = keys
      .map((k) => cleaned[k])
      .filter((v) => v != null && v !== "");

    if (!vals.length) continue;

    const raw = vals.length > 1 ? vals.join(" ") : vals[0];
    let value = parser ? parser(raw) : raw;

    if (group) {
      // Lookup MasterData key using cache
      const key = await getCachedMasterDataValue(group, raw);
      if (key !== null) {
        value = key;
      } else {
        // fallback to model's default value if MasterData entry not found
        const attr = model.rawAttributes[field];
        if (attr) {
          if (attr.defaultValue !== undefined) {
            value = attr.defaultValue;
          } else if (attr.default !== undefined) {
            value = attr.default;
          }
        }
      }
    }

    payload[field] = value;
  }

  return payload;
}

/**
 * Turn a raw HL7 message (pipe|^/&-delimited) into
 * an object like { 'PID.3.1': '10006579', 'PID.5': 'Smith', … }
 * ALWAYS split every field into components, even if there's no caret (`^`).
 */
function parseHL7Message(message) {
  const cleaned = {};
  const segments = message.trim().split(/\r?\n/).filter(Boolean);

  for (const line of segments) {
    // grab the 3-char segment name
    const segName = line.substr(0, 3);

    // build a fields[] array where fields[0] is the HL7 field-1, fields[1] ⇒ field-2, etc.
    let fields;
    if (segName === "MSH") {
      // MSH-1 is the 4th character of the line (the field separator)
      const fieldSep = line.charAt(3);
      // split by that exact character
      const parts = line.split(fieldSep);
      // parts[0] === "MSH", parts[1] === encoding chars, parts[2] === sending app, …
      // re-insert the separator itself as field-1
      fields = [fieldSep, ...parts.slice(1)];
    } else {
      // for everything else, HL7 always uses "|" as the separator
      fields = line.split("|").slice(1);
    }

    // now fields[0] → segName.1, fields[1] → segName.2, etc.
    fields.forEach((rawField, fi) => {
      const fieldPos = fi + 1; // HL7 field number
      const repetitionKey = `${segName}.${fieldPos}`;

      rawField
        .split("~") // repetitions
        .forEach((rep, rIdx) => {
          const repPrefix = repetitionKey + (rIdx ? `.${rIdx + 1}` : "");
          cleaned[repPrefix] = rep;

          // always split into components, even if no "^"
          rep.split("^").forEach((comp, ci) => {
            const compPrefix = `${repPrefix}.${ci + 1}`;
            if (!comp.includes("&")) {
              cleaned[compPrefix] = comp;
            } else {
              // sub-components
              comp.split("&").forEach((sub, si) => {
                cleaned[`${compPrefix}.${si + 1}`] = sub;
              });
            }
          });
        });
    });
  }

  return cleaned;
}

module.exports = {
  parseHL7Date,
  isDateType,
  buildSpec,
  buildPayload,
  parseHL7Message,
};
