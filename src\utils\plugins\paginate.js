const defaultPaginationOptions = {
  page: 1,
  limit: 10,
  order: [['createdAt', 'DESC']],
};

/**
 * Check if the input is a Sequelize model (has findAndCountAll).
 */
function isSequelizeModel(model) {
  return typeof model.findAndCountAll === 'function';
}

/**
 * Paginate Sequelize models or plain arrays.
 *
 * @param {object|array} modelOrArray - Sequelize model (with findAndCountAll) OR an array.
 * @param {object} queryOptions - Sequelize query options (only used if model).
 * @param {object} paginationOptions - { page, limit, sortBy, sortOrder }
 */
async function paginate(modelOrArray, queryOptions = {}, paginationOptions = {}) {
  // Merge defaults
  const options = { ...defaultPaginationOptions, ...paginationOptions };

  const page = parseInt(options.page, 10) || defaultPaginationOptions.page;
  const limit = (parseInt(options.limit, 10) > 100 ? 100 : parseInt(options.limit, 10)) || defaultPaginationOptions.limit;
  const offset = (page - 1) * limit;

  const order = [];
  if (options.sortBy && options.sortOrder) {
    order.push([options.sortBy, options.sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC']);
  } else {
    order.push(...options.order);
  }

  // 👉 Check if it's a Sequelize model
  if (isSequelizeModel(modelOrArray)) {
    const findOptions = {
      ...queryOptions,
      limit,
      offset,
      order,
    };

    const { count, rows } = await modelOrArray.findAndCountAll(findOptions);

    const totalPages = Math.ceil(count / limit);

    return {
      totalItems: count,
      totalPages,
      currentPage: page,
      data: rows,
    };
  }

  // 👉 Else treat it as an array
  const array = modelOrArray;
  const paginatedItems = array.slice(offset, offset + limit);
  const totalPages = Math.ceil(array.length / limit);

  return {
    totalItems: array.length,
    totalPages,
    currentPage: page,
    data: paginatedItems,
  };
}

module.exports = {
  paginate,
  defaultPaginationOptions,
};