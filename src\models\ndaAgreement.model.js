const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const NdaAgreement = sequelize.define(
    "NdaAgreement",
    {
      nda_agreement_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "CASCADE",
      },
      nda_template_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "nda_template",
          key: "nda_template_id",
        },
        onDelete: "CASCADE",
      },
      effective_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      expiration_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      signed_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "nda_agreement",
      timestamps: true,
      underscored: true,
    }
  );

  NdaAgreement.associate = (models) => {
    NdaAgreement.belongsTo(models.NdaTemplate, {
      foreignKey: "nda_template_id",
      as: "template",
    });

    NdaAgreement.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      as: "identity",
    });

    NdaAgreement.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "nda_agreement_status_name",
      constraints: false,
      scope: {
        group: "nda_agreement_status",
      },
    });
  };

  history(NdaAgreement, sequelize, DataTypes);

  return NdaAgreement;
};
