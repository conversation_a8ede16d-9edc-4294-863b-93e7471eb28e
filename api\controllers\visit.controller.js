const { Visit, Guest, GuestVisit, sequelize } = require("../models");
const httpStatus = require("http-status");
const catchAsync = require("../utils/catchAsync");
const { sendSuccess, sendError } = require("../utils/ApiResponse");

/**
 * @desc Create a visit, guest, and guest_visit in a single transaction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing the created visit, guest, and guest_visit
 */
exports.createVisitWithGuest = catchAsync(async (req, res) => {
  const t = await sequelize.transaction();
  try {
    // Extract visit and guest data from request body
    const {
      facility_id,
      host_id,
      escort_id,
      start_date,
      start_time,
      duration,
      guest: {
        first_name,
        last_name,
        date_of_birth,
        email,
        mobile_phone,
        image
      }
    } = req.body;

    // 1. Create Visit
    const visit = await Visit.create({
      facility_id,
      host_id,
      escort_id,
      start_date,
      start_time,
      duration,
      type: 1 // set type as 1
    }, { transaction: t });

    // 2. Create Guest
    const guest = await Guest.create({
      first_name,
      last_name,
      date_of_birth,
      email,
      mobile_phone,
      image
    }, { transaction: t });

    // 3. Create GuestVisit
    const guestVisit = await GuestVisit.create({
      guest_id: guest.guest_id,
      visit_id: visit.visit_id
    }, { transaction: t });

    await t.commit();
    sendSuccess(res, "Visit, Guest, and GuestVisit created successfully", httpStatus.CREATED, {
      visit,
      guest,
      guestVisit
    });
  } catch (error) {
    await t.rollback();
    sendError(res, error.message || "Failed to create visit with guest", httpStatus.BAD_REQUEST);
  }
});

/**
 * @desc Create a visit with extra fields and optionally link an existing guest via guest_id in guest_visit
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing the created visit and guestVisit (if guest_id provided)
 */
exports.createEventVisit = catchAsync(async (req, res) => {
  const t = await sequelize.transaction();
  try {
    const {
      title,
      category,
      start_date,
      end_date,
      repeat_visit,
      facility_id,
      access_level_id,
      host,
      check_in_instruction,
      escort_id,
      send_notification,
      remind_me,
      message_to_visitor,
      guest_id // optional
    } = req.body;

    // 1. Create Visit with type 0
    const visit = await Visit.create({
      title,
      type: 0,
      category,
      start_date,
      end_date,
      repeat_visit,
      facility_id,
      access_level_id,
      host_id,
      check_in_instruction,
      escort_id,
      send_notification,
      remind_me,
      message_to_visitor
    }, { transaction: t });

    let guestVisit = null;
    if (guest_id) {
      // 2. If guest_id provided, create GuestVisit
      guestVisit = await GuestVisit.create({
        guest_id,
        visit_id: visit.visit_id
      }, { transaction: t });
    }

    await t.commit();
    sendSuccess(res, "Event Visit created successfully", httpStatus.CREATED, {
      visit,
      guestVisit
    });
  } catch (error) {
    await t.rollback();
    sendError(res, error.message || "Failed to create event visit", httpStatus.BAD_REQUEST);
  }
});

/**
 * @desc Get visit summary with host/escort names
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing visit summary
 */
exports.getVisitSummary = catchAsync(async (req, res) => {
  const { visit_id } = req.params;
  const visit = await Visit.findByPk(visit_id, {
    attributes: [
      "title",
      "category",
      "type",
      "host_id",
      "escort_id",
      "start_date",
      "end_date",
      "status"
    ],
    include: [
      {
        model: require("../models").Identity,
        as: "host",
        attributes: [[require("../models").sequelize.literal("CONCAT(first_name, ' ', last_name)"), "host_name"]],
      },
      {
        model: require("../models").Identity,
        as: "escort",
        attributes: [[require("../models").sequelize.literal("CONCAT(first_name, ' ', last_name)"), "escort_name"]],
      }
    ]
  });
  if (!visit) {
    return sendError(res, "Visit not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Visit summary retrieved successfully", httpStatus.OK, visit);
});