// module.exports.authController = require('./auth.controller');
// module.exports.facilityController = require('./facility.controller');

const fs = require("fs");
const path = require("path");
const { titleCase } = require("../utils/helpers");

const controllers = {};

// Get the current directory name (controllers directory)
const controllersDirectory = __dirname;

// Read all files in the controllers directory
fs.readdirSync(controllersDirectory)
  .filter((file) => file.endsWith(".controller.js")) // Only consider files ending with '.controller.js'
  .forEach((file) => {
    const controllerName = titleCase(path.basename(file, ".controller.js")); // Remove the .controller.js extension for the controller name
    controllers[`${controllerName}Controller`] = require(path.join(controllersDirectory, file));
  });

// Set up associations (if defined in controllers)
Object.keys(controllers).forEach((controllerName) => {
  if (controllers[controllerName].associate) {
    controllers[controllerName].associate(controllers);
  }
});

module.exports = controllers;
