#!/usr/bin/env node

/**
 * CSV Test Data Generator for HR Data Performance Testing
 * Generates realistic HR data aligned with processor/mappings/hrData.mapping.json
 * for testing agent and processor performance with parseData function
 * 
 * Field mapping follows hrData.mapping.json structure:
 * - Uses columnName mapping type
 * - Required fields: email, firstName, lastName
 * - Maps to Identity model fields via processor parseData function
 */

const fs = require('fs');
const path = require('path');
const csv = require('fast-csv');
const { faker } = require('@faker-js/faker');

class CSVGenerator {
  constructor() {
    // Predefined data for consistency
    this.companies = [
      'TechCorp Inc',
      'Digital Dynamics',
      'Innovation Labs',
      'Global Solutions Ltd',
      'Future Systems'
    ];

    this.locations = [
      'New York',
      'Los Angeles',
      'Chicago',
      'San Francisco',
      'Boston',
      'Seattle',
      'Austin'
    ];

    this.departments = [
      'Engineering',
      'Marketing',
      'Sales',
      'Operations',
      'Finance',
      'HR',
      'Legal',
      'IT',
      'Research',
      'Product',
      'Customer Service'
    ];

    this.jobTitles = {
      'Engineering': ['Software Engineer', 'Senior Engineer', 'Tech Lead', 'Engineering Manager', 'DevOps Engineer'],
      'Marketing': ['Marketing Manager', 'Digital Marketer', 'Content Creator', 'SEO Specialist', 'Brand Manager'],
      'Sales': ['Sales Representative', 'Account Manager', 'Sales Director', 'Business Development', 'Sales Coordinator'],
      'Operations': ['Operations Manager', 'Process Analyst', 'Supply Chain Manager', 'Operations Coordinator'],
      'Finance': ['Financial Analyst', 'Accountant', 'Controller', 'Finance Manager', 'Budget Analyst'],
      'HR': ['HR Manager', 'Recruiter', 'Benefits Administrator', 'Training Specialist', 'HR Coordinator'],
      'Legal': ['Legal Counsel', 'Paralegal', 'Compliance Officer', 'Contract Manager'],
      'IT': ['IT Manager', 'System Administrator', 'Network Engineer', 'IT Support', 'Security Analyst'],
      'Research': ['Research Analyst', 'Lab Technician', 'Data Scientist', 'Research Manager'],
      'Product': ['Product Manager', 'Product Analyst', 'Product Owner', 'Product Coordinator', 'UX Designer'],
      'Customer Service': ['Support Representative', 'Customer Success', 'Technical Support', 'Support Manager']
    };
  }

  /**
   * Generate a single HR record aligned with hrData.mapping.json
   * @param {number} index - Record index for unique identification
   * @returns {Object} HR record object matching processor expectations
   */
  generateRecord(index) {
    const department = faker.helpers.arrayElement(this.departments);
    const jobTitle = faker.helpers.arrayElement(this.jobTitles[department]);
    const company = faker.helpers.arrayElement(this.companies);
    const location = faker.helpers.arrayElement(this.locations);

    // Generate consistent employee ID based on index
    const employeeId = `EMP${String(index).padStart(6, '0')}`;

    // Generate identity type based on master data: 0=COS, 1=EMP
    const identityType = faker.helpers.weightedArrayElement([
      { weight: 20, value: 0 }, // 20% COS (Contractor/Consultant)
      { weight: 80, value: 1 }  // 80% EMP (Employee)
    ]);

    // Generate status based on master data: 0=Active, 1=Terminated, 2=Suspended
    const status = faker.helpers.weightedArrayElement([
      { weight: 85, value: 0 }, // 85% Active
      { weight: 10, value: 1 }, // 10% Terminated  
      { weight: 5, value: 2 }   // 5% Suspended
    ]);

    // Generate proper dates - avoid empty strings that cause DB errors
    const startDate = faker.date.past({ years: 5 }).toISOString().split('T')[0];

    // Build the record object
    const record = {
      // Required fields (as per hrData.mapping.json validation)
      email: faker.internet.email().toLowerCase(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),

      // Optional fields mapped in hrData.mapping.json
      middleName: faker.helpers.maybe(() => faker.person.middleName(), { probability: 0.3 }) || '',
      employeeId: employeeId,
      identityType: identityType,
      nationalId: faker.helpers.maybe(() => faker.string.alphanumeric(9), { probability: 0.7 }) || '',
      suffix: faker.helpers.maybe(() => faker.person.suffix(), { probability: 0.1 }) || '',
      mobile: faker.helpers.maybe(() => faker.number.int({ max: 9999999999 }), { probability: 0.8 }) || '',
      startDate: startDate,
      status: status,
      company: company,
      organization: `${company} - ${location}`,
      companyCode: company.replace(/[^A-Z]/g, '').substring(0, 4),
      jobTitle: jobTitle,
      jobCode: `${department.substring(0, 3).toUpperCase()}${faker.number.int({ min: 100, max: 999 })}`
    };

    // Handle endDate properly - processor will convert empty strings to null
    if (status === 1) {
      // Terminated employees may have end dates
      const shouldHaveEndDate = faker.helpers.maybe(() => true, { probability: 0.8 });
      if (shouldHaveEndDate) {
        record.endDate = faker.date.recent().toISOString().split('T')[0];
      } else {
        // Use empty string - processor will convert to null
        record.endDate = '';
      }
    } else {
      // Active/suspended employees don't have end dates
      record.endDate = '';
    }

    return record;
  }

  /**
   * Generate CSV file with specified number of records
   * @param {number} recordCount - Number of records to generate
   * @param {string} filename - Output filename
   * @returns {Promise} Promise that resolves when file is written
   */
  async generateCSV(recordCount, filename) {
    const outputDir = 'E:\\Windows\\Desktop\\care\\csv';

    // Ensure output directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const outputPath = path.join(outputDir, filename);
    const records = [];

    // Generate records
    console.log(`Generating ${recordCount} HR records...`);
    for (let i = 1; i <= recordCount; i++) {
      records.push(this.generateRecord(i));

      // Progress indicator for large datasets
      if (i % 1000 === 0) {
        console.log(`Generated ${i}/${recordCount} records...`);
      }
    }

    // Write to CSV
    return new Promise((resolve, reject) => {
      const writeStream = fs.createWriteStream(outputPath);

      csv.write(records, { headers: true })
        .pipe(writeStream)
        .on('error', reject)
        .on('finish', () => {
          console.log(`✅ Generated ${recordCount} records in ${filename}`);
          resolve();
        });
    });
  }

  /**
   * Generate updated CSV with mixed fresh and updated records
   * @param {string} initialFilename - Initial CSV filename
   * @param {string} updatedFilename - Updated CSV filename  
   * @param {number} updatePercentage - Percentage of records to update (0-100)
   * @param {number} newRecordPercentage - Percentage of new records to add (0-100)
   * @returns {Promise} Promise that resolves when file is written
   */
  async generateUpdatedCSV(initialFilename, updatedFilename, updatePercentage = 25, newRecordPercentage = 5) {
    const outputDir = 'E:\\Windows\\Desktop\\care\\csv';
    const initialPath = path.join(outputDir, initialFilename);
    const updatedPath = path.join(outputDir, updatedFilename);

    // Read initial CSV
    const records = [];

    return new Promise((resolve, reject) => {
      fs.createReadStream(initialPath)
        .pipe(csv.parse({ headers: true }))
        .on('data', (row) => records.push(row))
        .on('end', async () => {
          try {
            const totalRecords = records.length;
            const recordsToUpdate = Math.floor(totalRecords * updatePercentage / 100);
            const newRecordsToAdd = Math.floor(totalRecords * newRecordPercentage / 100);

            // Select random records to update
            const indicesToUpdate = faker.helpers.arrayElements(
              Array.from({ length: totalRecords }, (_, i) => i),
              recordsToUpdate
            );

            const stats = {
              totalRecords: totalRecords + newRecordsToAdd,
              updatedRecords: 0,
              newRecords: newRecordsToAdd,
              unchangedRecords: totalRecords - recordsToUpdate
            };

            // Update selected records
            indicesToUpdate.forEach(index => {
              const record = records[index];

              // Update some fields to simulate real changes
              // Find department from jobTitle to get appropriate new job titles
              let department = 'Engineering'; // default
              for (const [dept, titles] of Object.entries(this.jobTitles)) {
                if (titles.includes(record.jobTitle)) {
                  department = dept;
                  break;
                }
              }

              record.jobTitle = faker.helpers.arrayElement(this.jobTitles[department] || ['Updated Position']);
              record.mobile = faker.helpers.maybe(() => faker.phone.number('##########'), { probability: 0.8 }) || '';
              record.nationalId = faker.helpers.maybe(() => faker.string.alphanumeric(9), { probability: 0.7 }) || '';
              record.status = faker.helpers.weightedArrayElement([
                { weight: 85, value: 0 }, // 85% Active
                { weight: 10, value: 1 }, // 10% Terminated  
                { weight: 5, value: 2 }   // 5% Suspended
              ]);

              stats.updatedRecords++;
            });

            // Add new records
            const maxEmployeeId = Math.max(...records.map(r => parseInt(r.employeeId.replace('EMP', ''))));
            for (let i = 1; i <= newRecordsToAdd; i++) {
              records.push(this.generateRecord(maxEmployeeId + i));
            }

            // Write updated CSV
            const writeStream = fs.createWriteStream(updatedPath);

            csv.write(records, { headers: true })
              .pipe(writeStream)
              .on('error', reject)
              .on('finish', () => {
                console.log(`✅ Generated updated CSV with ${updatePercentage}% changes`);
                console.log(`   Total: ${stats.totalRecords}, Updated: ${stats.updatedRecords}, New: ${stats.newRecords}, Unchanged: ${stats.unchangedRecords}`);
                resolve();
              });

          } catch (error) {
            reject(error);
          }
        })
        .on('error', reject);
    });
  }
}

// Export for use as module
module.exports = CSVGenerator;

// CLI execution
if (require.main === module) {
  const generator = new CSVGenerator();

  // Parse command line arguments
  const args = process.argv.slice(2);

  if (args.length > 0) {
    // Handle specific record count: node csv-generator.js <count>
    const recordCount = parseInt(args[0]);

    if (isNaN(recordCount) || recordCount <= 0) {
      console.error('❌ Error: Please provide a valid number of records');
      console.log('Usage: node csv-generator.js <record_count>');
      console.log('Example: node csv-generator.js 10000');
      process.exit(1);
    }

    const filename = `hr-data-${recordCount}-records.csv`;

    console.log(`🚀 Generating ${recordCount} HR records...`);
    generator.generateCSV(recordCount, filename)
      .then(() => {
        console.log(`✅ Successfully generated ${filename} with ${recordCount} records`);
        console.log(`📁 Output location: E:\\Windows\\Desktop\\care\\csv\\${filename}`);
      })
      .catch(console.error);

  } else {
    // Default behavior: generate all test scenarios
    const scenarios = [
      { name: 'small-fresh', count: 1000, updates: false },
      { name: 'small-mixed', count: 1000, updates: true, updatePct: 25, newPct: 2.5 },
      { name: 'medium-fresh', count: 5000, updates: false },
      { name: 'medium-mixed', count: 5000, updates: true, updatePct: 25, newPct: 2.5 },
      { name: 'large-fresh', count: 10000, updates: false },
      { name: 'large-mixed', count: 10000, updates: true, updatePct: 25, newPct: 2.5 },
      { name: 'large-heavy-updates', count: 10000, updates: true, updatePct: 50, newPct: 5 }
    ];

    async function generateAllScenarios() {
      console.log('🚀 Starting CSV generation for all test scenarios...\n');

      const generatedFiles = [];

      for (const scenario of scenarios) {
        console.log(`📊 Generating scenario: ${scenario.name}`);

        const initialFilename = `${scenario.name}-initial.csv`;
        await generator.generateCSV(scenario.count, initialFilename);
        generatedFiles.push(`  📄 ${scenario.name}: ${initialFilename}`);

        if (scenario.updates) {
          const updatedFilename = `${scenario.name}-updated-${scenario.updatePct}pct.csv`;
          await generator.generateUpdatedCSV(initialFilename, updatedFilename, scenario.updatePct, scenario.newPct);
          generatedFiles.push(`     ${updatedFilename}`);
        }

        console.log('');
      }

      console.log('✅ All test scenarios generated successfully!\n');
      console.log('Generated files:');
      generatedFiles.forEach(file => console.log(file));
    }

    generateAllScenarios().catch(console.error);
  }
}
