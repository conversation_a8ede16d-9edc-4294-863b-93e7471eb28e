const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const PatientAddress = sequelize.define(
    "PatientAddress",
    {
      address_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      patient_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "patient",
          key: "patient_id",
        },
        onDelete: "CASCADE",
      },
      address_line_1: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      address_line_2: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      country_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "country",
          key: "country_id",
        },
      },
      state_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "state",
          key: "state_id",
        },
      },
      postal_code: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "patient_address",
      timestamps: true,
      underscored: true,
    }
  );

  PatientAddress.associate = (models) => {
    PatientAddress.belongsTo(models.Patient, {
      foreignKey: "patient_id",
      as: "patient",
      onDelete: "CASCADE",
    });
    PatientAddress.belongsTo(models.Country, {
      foreignKey: "country_id",
      as: "country",
    });
    PatientAddress.belongsTo(models.State, {
      foreignKey: "state_id",
      as: "state",
    });
  };

  history(PatientAddress, sequelize, DataTypes);

  return PatientAddress;
};
