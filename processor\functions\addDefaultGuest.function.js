const logger = require("../config/logger");
const { sequelize, Appointment, AppointmentGuest, PatientGuest } = require("../models");
const { generateOTP } = require("../utils/helpers");

/**
 * Adds default guests for a given appointment event.
 *
 * @param {Object} eventInstance - The event instance containing appointment reference.
 * @param {string|number} eventInstance.parent_id - ID of the appointment to add guests for.
 * @param {Object} context - Execution context with tracing information.
 * @param {string} context.trace_id - Unique trace identifier for logging.
 * @returns {Promise<void>} Resolves when guests have been added, or rejects on error.
 * @throws {Error} If the appointment is not found or any database operation fails.
 */
const addDefaultGuest = async (eventInstance, context) => {
  const traceId = context.trace_id;
  logger.info(`[TRACE ${traceId}] Starting add default guests of patient`);

  const transaction = await sequelize.transaction();
  try {
    const appointment = await Appointment.findOne({
      where: { appointment_id: eventInstance.parent_id },
      transaction,
    });
    if (!appointment) {
      throw new Error(`Appointment with id ${eventInstance.parent_id} not found`);
    }

    // Retrieve all non-denied guest records for the patient on this appointment
    const guests = await PatientGuest.findAll({
      where: { patient_id: appointment.patient_id, denied_on: null },
      transaction,
    });

    // Build an array of appointment-guest objects
    const appointmentGuests = guests.map(guest => ({
      appointment_id:   appointment.appointment_id,
      patient_guest_id: guest.patient_guest_id,
      facility:         appointment.facility_id,
      screening:        appointment.screening,
      guest_pin: generateOTP(6),
      status:           4   // registered
    }));
  
    // Bulk-insert in a single query
    await AppointmentGuest.bulkCreate(appointmentGuests, { transaction });
    // Commit transaction on success
    await transaction.commit();
    logger.info(`[TRACE ${traceId}] Completed add default guests of patient`);
  } catch (error) {
    // Rollback transaction on any failure
    await transaction.rollback();
    logger.error(`[TRACE ${traceId}] Transaction rolled back: ${error.message}`);
    throw error;
  }
};

module.exports = addDefaultGuest;
