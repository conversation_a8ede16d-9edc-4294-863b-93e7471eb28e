const passport = require('../config/passport'); 
const { status: httpStatus } = require("http-status");
const ApiError = require("../utils/ApiError");

// The verifyCallback now loads the identity's role and its permissions dynamically.
const verifyCallback = (req, resolve, reject, requiredRights) => async (err, identity, info) => {
    if (err || info || !identity) {
      return reject(
        new ApiError(httpStatus.UNAUTHORIZED, "Please authenticate")
      );
    }

    req.identity = identity;

    if (requiredRights.length) {
      // Load all roles with their permissions
      const roles = await identity.getRole({
        include: [
          {
            association: "permission",
            attributes: ["name"],
            through: { attributes: [] }, // Exclude join table attributes
          },
        ],
      });

      if (!roles || roles.length === 0) {
        return reject(
          new ApiError(httpStatus.FORBIDDEN, "No roles assigned to identity")
        );
      }

      // Aggregate permissions from all roles
      const identityRights = roles.flatMap((role) =>
        role.permission.map((permission) => permission.name)
      );

      // Check required rights
      const hasRequiredRights = requiredRights.every((requiredRight) =>
        identityRights.includes(requiredRight)
      );

      if (
        !hasRequiredRights &&
        req.params.identityId !== identity.identity_id
      ) {
        return reject(new ApiError(httpStatus.FORBIDDEN, "Unauthorized access of identity"));
      }
    }

    resolve();
  };

const auth = (...requiredRights) =>
  async (req, res, next) => {
    return new Promise((resolve, reject) => {
      passport.authenticate(
        "custom",
        { session: false },
        verifyCallback(req, resolve, reject, requiredRights)
      )(req, res, next);
    })
      .then(() => next())
      .catch((err) => next(err));
  };

module.exports = auth;
