{"name": "caremate", "version": "1.0.0", "description": "Caremate project setup", "repository": {"type": "git", "url": "https://git.onetalkhub.com/care/caremate.git"}, "license": "ISC", "author": "<PERSON><PERSON><PERSON>", "type": "commonjs", "main": "setup.js", "scripts": {"setup": "node setup.js --env-file .env.local", "setup:unlink": "node setup.js --env-file .env.local --unlink", "test": "echo \"Error: no test specified\" && exit 1", "csv": "node test-utils/csv-generator.js", "csv:1000": "node test-utils/csv-generator.js 1000", "csv:5000": "node test-utils/csv-generator.js 5000", "csv:10000": "node test-utils/csv-generator.js 10000"}, "dependencies": {"@aws-sdk/client-s3": "^3.812.0", "@azure/storage-blob": "^12.27.0", "@faker-js/faker": "^9.8.0", "@keyv/memcache": "^2.0.1", "@keyv/redis": "^4.3.2", "amqplib": "^0.10.5", "axios": "^1.9.0", "basic-ftp": "^5.0.5", "bcrypt": "^5.1.1", "cache-manager": "^6.4.1", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csurf": "^1.10.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "fast-csv": "^5.0.2", "helmet": "^8.0.0", "http-status": "^2.1.0", "joi": "^17.13.3", "json-rules-engine": "^7.3.1", "jsonwebtoken": "^9.0.2", "keyv": "^5.3.2", "lodash": "^4.17.21", "morgan": "^1.10.0", "node-cron": "^4.0.6", "p-limit": "^6.2.0", "passport": "^0.7.0", "passport-azure-ad": "^4.3.5", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-oauth2": "^1.8.0", "passport-openidconnect": "^0.1.2", "passport-saml": "^3.2.4", "pg": "^8.13.1", "rabbitmq-stream-js-client": "^0.6.1", "rate-limiter-flexible": "^5.0.5", "redis": "^4.7.0", "sequelize": "^6.37.5", "sharp": "^0.34.1", "ssh2-sftp-client": "^12.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.0.5", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"chai": "^5.1.2", "jest": "^29.7.0", "mocha": "^11.1.0", "nodemon": "^3.1.9", "sinon": "^19.0.2", "supertest": "^7.0.0", "symlink-dir": "^6.0.5"}}