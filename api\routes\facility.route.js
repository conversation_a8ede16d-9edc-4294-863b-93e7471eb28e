const express = require("express");
const validate = require("../middlewares/validate");
const { FacilityValidation } = require("../validations");
const { FacilityController } = require("../controllers");
const auth = require("../middlewares/auth");
const catchAsync = require("../utils/catchAsync");
const uploadToBase64 = require("../middlewares/upload");

/**
 * @swagger
 * tags:
 *   name: Facility Manager
 *   description: Facility management and retrieval
 */
const router = express.Router();

/**
 * @swagger
 * /facility:
 *   get:
 *     summary: Get all facilities (paginated)
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by (e.g., "name", "updatedAt"). Default is "updatedAt".
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Order of sorting (ASC for ascending, DESC for descending). Default is "DESC".
 *     responses:
 *       200:
 *         description: Paginated list of facilities with their addresses.
 *         content:
 *           application/json:
 *             example:
 *               totalItems: 50
 *               totalPages: 5
 *               currentPage: 1
 *               data:
 *                 - id: "64b8f0e2d123e4567890abcd"
 *                   name: "Central Hospital"
 *                   facility_code: "CH123"
 *                   facility_type: 1
 *                   status: 0
 *                   phone: "******-1234"
 *                   email: "<EMAIL>"
 *                   address:
 *                     address_line_1: "123 Main St"
 *                     country_id: "123e4567-e89b-12d3-a456-426614174000"
 *                     state_id: "123e4567-e89b-12d3-a456-426614174001"
 *                     postal_code: 90210
 */
router.get("/", auth("view_facilities"), catchAsync(FacilityController.index));
/**
 * @swagger
 * /facility/types:
 *   get:
 *     summary: Get facility types
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of facility types
 *         content:
 *           application/json:
 *             example:
 *               status: true
 *               message: "Facility types retrieved successfully"
 *               data:
 *                 - "Industrial"
 *                 - "Commercial"
 *                 - "Residential"
 *                 - "Institutional"
 *                 - "Recreational"
 */
router.get("/types", auth("view_facilities"), catchAsync(FacilityController.facility_type));
/**
 * @swagger
 * /facility/{facilityId}:
 *   get:
 *     summary: Get facility by ID
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID
 *     responses:
 *       200:
 *         description: Facility details with address
 *         content:
 *           application/json:
 *             example:
 *               id: "64b8f0e2d123e4567890abcd"
 *               name: "Central Hospital"
 *               facility_code: "CH123"
 *               facility_type: 1
 *               status: 0
 *               phone: "******-1234"
 *               email: "<EMAIL>"
 *               address:
 *                 address_line_1: "123 Main St"
 *                 country_id: "123e4567-e89b-12d3-a456-426614174000"
 *                 state_id: "123e4567-e89b-12d3-a456-426614174001"
 *                 postal_code: 90210
 *       404:
 *         description: Facility not found
 */
router.get(
  "/:facilityId",
  auth("facility_address_details"),
  validate(FacilityValidation.facility),
  catchAsync(FacilityController.show)
);

/**
 * @swagger
 * /facility:
 *   post:
 *     summary: Create a new facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             name: "Sunrise Clinic"
 *             facility_code: "SC567"
 *             facility_type: 1
 *             timezone_id: "123e4567-e89b-12d3-a456-426614174000"
 *             phone: "******-5678"
 *             email: "<EMAIL>"
 *             status: 0
 *             address:
 *               address_line_1: "456 Elm St"
 *               country_id: "123e4567-e89b-12d3-a456-426614174000"
 *               state_id: "123e4567-e89b-12d3-a456-426614174001"
 *               postal_code: 10001
 *     responses:
 *       201:
 *         description: Facility created successfully
 */
router.post(
  "/",
  auth("create_facility_address"),
  validate(FacilityValidation.create),
  catchAsync(FacilityController.create)
);

/**
 * @swagger
 * /facility/{facilityId}:
 *   patch:
 *     summary: Update a facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             name: "Updated Hospital Name"
 *             facility_code: "UH123"
 *             status: 0
 *     responses:
 *       200:
 *         description: Facility updated successfully
 */
router.patch(
  "/:facilityId",
  auth("edit_facility"),
  validate(FacilityValidation.update),
  catchAsync(FacilityController.update)
);

/**
 * @swagger
 * /facility/{facilityId}/status:
 *   patch:
 *     summary: Change facility status
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             status: 1
 *     responses:
 *       200:
 *         description: Facility status updated successfully
 */
router.patch(
  "/:facilityId/status",
  auth("status_facility"),
  validate(FacilityValidation.status),
  catchAsync(FacilityController.status)
);

/**
 * @swagger
 * /facility/{facilityId}/address:
 *   patch:
 *     summary: Update facility address
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             address_line_1: "789 Oak St"
 *             country_id: "123e4567-e89b-12d3-a456-426614174000"
 *             state_id: "123e4567-e89b-12d3-a456-426614174001"
 *             postal_code: 75001
 *     responses:
 *       200:
 *         description: Facility address updated successfully
 */
router.patch(
  "/:facilityId/address",
  auth("edit_address"),
  validate(FacilityValidation.address),
  catchAsync(FacilityController.address)
);

/**
 * @swagger
 * /facility/{facilityId}/image:
 *   patch:
 *     summary: Update the facility image
 *     description: Update the image associated with the facility using the media plugin.
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Image file to upload
 *     responses:
 *       200:
 *         description: Facility image updated successfully.
 *         content:
 *           application/json:
 *             example:
 *               id: 1
 *               image: "https://example.com/new-image.jpg"
 *       404:
 *         description: Facility not found.
 */
router.patch(
  "/:facilityId/image",
  auth("edit_facility"),
  uploadToBase64("image"),
  validate(FacilityValidation.image),
  catchAsync(FacilityController.image)
);

module.exports = router;