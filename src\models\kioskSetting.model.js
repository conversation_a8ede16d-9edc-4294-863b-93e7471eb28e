const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const KioskSetting = sequelize.define(
    "KioskSetting",
    {
      kiosk_setting_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      config_key: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
      },
      config_group: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(150),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      default_config_value: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      masterdata_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "master_data",
          key: "master_data_id",
        },
      },
    },
    {
      tableName: "kiosk_setting",
      timestamps: true,
      underscored: true,
    }
  );

  KioskSetting.associate = (models) => {
    KioskSetting.belongsTo(models.MasterData, {
      foreignKey: "masterdata_id",
      as: "masterdata",
    });
  };

  history(KioskSetting, sequelize, DataTypes);

  return KioskSetting;
};
