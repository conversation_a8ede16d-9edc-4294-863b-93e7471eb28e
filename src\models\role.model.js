const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const Role = sequelize.define(
    "Role",
    {
      role_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        set(value) {
          if (typeof value === "string") this.setDataValue("name", value.trim());
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "role",
      timestamps: true,
      underscored: true,
    }
  );

  Role.associate = (models) => {
    // Many-to-many: Role <-> Identity via IdentityRole mapping table
    Role.belongsToMany(models.Identity, {
      through: models.IdentityRole,
      foreignKey: "role_id",
      otherKey: "identity_id",
      as: "identity",
    });
    // Many-to-many: Role <-> Permission via RolePermission mapping table
    Role.belongsToMany(models.Permission, {
      through: models.RolePermission,
      foreignKey: "role_id",
      otherKey: "permission_id",
      as: "permission",
    });
  };

  history(Role, sequelize, DataTypes);

  return Role;
};
